import React, { useState, useContext, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import { 
  getMessages, 
  sendMessage, 
  getChatRoom,
  getSocket,
  sendTypingIndicator,
  markAllMessagesAsRead,
  setupSocketListeners,
  removeSocketListeners
} from '../../api/chat/chatApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';

const { width } = Dimensions.get('window');

const ChatRoom = () => {
  const { theme } = useContext(ThemeContext);
  const { user } = useContext(AuthContext);
  const router = useRouter();
  const queryClient = useQueryClient();
  const { roomId, roomName, roomType } = useLocalSearchParams();
  
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState([]);
  const [replyingTo, setReplyingTo] = useState(null);
  const flatListRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Fetch chat room details
  const { data: chatRoom } = useQuery({
    queryKey: ['chatRoom', roomId],
    queryFn: () => getChatRoom(roomId),
    enabled: !!roomId,
  });

  // Fetch messages
  const {
    data: messages = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['messages', roomId],
    queryFn: () => getMessages(roomId),
    enabled: !!roomId,
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: (messageData) => sendMessage(roomId, messageData),
    onSuccess: (newMessage) => {
      // Add message to local state immediately
      queryClient.setQueryData(['messages', roomId], (oldData) => {
        return [...(oldData || []), newMessage];
      });
      
      // Clear input and reply
      setMessage('');
      setReplyingTo(null);
      
      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    },
    onError: (error) => {
      showToast('error', 'Error', 'Failed to send message');
    },
  });

  // Setup socket listeners for real-time updates
  useEffect(() => {
    const socket = getSocket();
    if (!socket) return;

    const socketCallbacks = {
      onMessage: (newMessage) => {
        if (newMessage.roomId === roomId) {
          queryClient.setQueryData(['messages', roomId], (oldData) => {
            const exists = oldData?.some(msg => msg.id === newMessage.id);
            if (exists) return oldData;
            return [...(oldData || []), newMessage];
          });
          
          // Auto-scroll if user is near bottom
          setTimeout(() => {
            flatListRef.current?.scrollToEnd({ animated: true });
          }, 100);
        }
      },
      
      onUserTyping: ({ roomId: typingRoomId, userId, userName }) => {
        if (typingRoomId === roomId && userId !== user.id) {
          setTypingUsers(prev => {
            const filtered = prev.filter(u => u.userId !== userId);
            return [...filtered, { userId, userName }];
          });
        }
      },
      
      onUserStoppedTyping: ({ roomId: typingRoomId, userId }) => {
        if (typingRoomId === roomId) {
          setTypingUsers(prev => prev.filter(u => u.userId !== userId));
        }
      },
      
      onMessageRead: ({ roomId: readRoomId, messageId, userId }) => {
        if (readRoomId === roomId) {
          queryClient.setQueryData(['messages', roomId], (oldData) => {
            return oldData?.map(msg => {
              if (msg.id === messageId) {
                return {
                  ...msg,
                  readBy: [...(msg.readBy || []), { userId, readAt: new Date() }]
                };
              }
              return msg;
            });
          });
        }
      },
      
      onError: (error) => {
        console.error('Socket error:', error);
      },
    };

    setupSocketListeners(socketCallbacks);

    return () => {
      removeSocketListeners();
    };
  }, [roomId, user.id, queryClient]);

  // Mark messages as read when entering room
  useEffect(() => {
    if (roomId) {
      markAllMessagesAsRead(roomId).catch(console.error);
    }
  }, [roomId]);

  // Handle typing indicator
  const handleTyping = useCallback((text) => {
    setMessage(text);
    
    if (!isTyping && text.length > 0) {
      setIsTyping(true);
      sendTypingIndicator(roomId, true);
    }
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      sendTypingIndicator(roomId, false);
    }, 1000);
  }, [roomId, isTyping]);

  const handleSendMessage = useCallback(() => {
    if (!message.trim()) return;
    
    const messageData = {
      content: message.trim(),
      type: 'text',
      replyTo: replyingTo?.id,
    };
    
    sendMessageMutation.mutate(messageData);
  }, [message, replyingTo, sendMessageMutation]);

  const handleImagePicker = useCallback(async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        showToast('error', 'Permission Required', 'Camera roll permission is required');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        quality: 0.8,
        allowsEditing: true,
      });

      if (!result.canceled) {
        const messageData = {
          type: 'image',
          file: {
            uri: result.assets[0].uri,
            type: 'image/jpeg',
            name: 'image.jpg',
          },
        };
        
        sendMessageMutation.mutate(messageData);
      }
    } catch (error) {
      showToast('error', 'Error', 'Failed to pick image');
    }
  }, [sendMessageMutation]);

  const handleFilePicker = useCallback(async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
      });

      if (!result.canceled) {
        const messageData = {
          type: 'file',
          file: result.assets[0],
        };
        
        sendMessageMutation.mutate(messageData);
      }
    } catch (error) {
      showToast('error', 'Error', 'Failed to pick file');
    }
  }, [sendMessageMutation]);

  const handleAttachmentPress = useCallback(() => {
    Alert.alert(
      'Send Attachment',
      'Choose attachment type',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Photo', onPress: handleImagePicker },
        { text: 'File', onPress: handleFilePicker },
      ]
    );
  }, [handleImagePicker, handleFilePicker]);

  const handleMessageLongPress = useCallback((messageItem) => {
    const isOwnMessage = messageItem.senderId === user.id;
    
    const options = [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Reply', onPress: () => setReplyingTo(messageItem) },
    ];
    
    if (isOwnMessage) {
      options.push({ text: 'Delete', style: 'destructive', onPress: () => {
        // Handle delete message
      }});
    }
    
    Alert.alert('Message Options', '', options);
  }, [user.id]);

  const formatTime = useCallback((timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  const renderMessage = ({ item, index }) => {
    const isOwnMessage = item.senderId === user.id;
    const showAvatar = !isOwnMessage && (
      index === messages.length - 1 || 
      messages[index + 1]?.senderId !== item.senderId
    );
    const showTimestamp = index === messages.length - 1 || 
      new Date(messages[index + 1]?.createdAt).getTime() - new Date(item.createdAt).getTime() > 300000; // 5 minutes

    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessageContainer : styles.otherMessageContainer
      ]}>
        {showAvatar && !isOwnMessage && (
          <Image
            source={{ uri: item.sender?.avatar || 'https://via.placeholder.com/30' }}
            style={styles.messageAvatar}
          />
        )}
        
        <TouchableOpacity
          style={[
            styles.messageBubble,
            isOwnMessage 
              ? [styles.ownMessageBubble, { backgroundColor: theme.PRIMARY }]
              : [styles.otherMessageBubble, { backgroundColor: theme.CARD_BACKGROUND }],
            !showAvatar && !isOwnMessage && styles.messageBubbleNoAvatar
          ]}
          onLongPress={() => handleMessageLongPress(item)}
          activeOpacity={0.8}
        >
          {item.replyTo && (
            <View style={[styles.replyContainer, { borderLeftColor: theme.SECONDARY }]}>
              <Text style={[styles.replyText, { color: theme.TEXT_SECONDARY }]} numberOfLines={1}>
                {item.replyTo.content}
              </Text>
            </View>
          )}
          
          {item.type === 'image' ? (
            <Image source={{ uri: item.fileUrl }} style={styles.messageImage} />
          ) : item.type === 'file' ? (
            <View style={styles.fileContainer}>
              <Ionicons name="document" size={24} color={isOwnMessage ? '#fff' : theme.PRIMARY} />
              <Text style={[
                styles.fileName,
                { color: isOwnMessage ? '#fff' : theme.TEXT_PRIMARY }
              ]}>
                {item.fileName}
              </Text>
            </View>
          ) : (
            <Text style={[
              styles.messageText,
              { color: isOwnMessage ? '#fff' : theme.TEXT_PRIMARY }
            ]}>
              {item.content}
            </Text>
          )}
          
          <Text style={[
            styles.messageTime,
            { color: isOwnMessage ? 'rgba(255,255,255,0.7)' : theme.TEXT_SECONDARY }
          ]}>
            {formatTime(item.createdAt)}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;
    
    return (
      <View style={styles.typingContainer}>
        <Text style={[styles.typingText, { color: theme.TEXT_SECONDARY }]}>
          {typingUsers.map(u => u.userName).join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
        </Text>
      </View>
    );
  };

  const renderReplyBar = () => {
    if (!replyingTo) return null;
    
    return (
      <View style={[styles.replyBar, { backgroundColor: theme.CARD_BACKGROUND }]}>
        <View style={styles.replyInfo}>
          <Text style={[styles.replyLabel, { color: theme.TEXT_SECONDARY }]}>
            Replying to {replyingTo.sender?.name}
          </Text>
          <Text style={[styles.replyPreview, { color: theme.TEXT_PRIMARY }]} numberOfLines={1}>
            {replyingTo.content}
          </Text>
        </View>
        <TouchableOpacity onPress={() => setReplyingTo(null)}>
          <Ionicons name="close" size={20} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>
      </View>
    );
  };

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <View style={styles.header}>
          <BackButton />
          <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>
            Error
          </Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.TEXT_PRIMARY }]}>
            Failed to load messages
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
            onPress={refetch}
          >
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <BackButton color="#fff" />
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle} numberOfLines={1}>
            {roomName}
          </Text>
          {chatRoom?.participants && (
            <Text style={styles.headerSubtitle}>
              {chatRoom.participants.length} participants
            </Text>
          )}
        </View>
        <TouchableOpacity>
          <Ionicons name="information-circle-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </LinearGradient>
      
      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id.toString()}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContainer}
        inverted={false}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: false })}
        showsVerticalScrollIndicator={false}
      />
      
      {/* Typing indicator */}
      {renderTypingIndicator()}
      
      {/* Reply bar */}
      {renderReplyBar()}
      
      {/* Input */}
      <View style={[styles.inputContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
        <TouchableOpacity
          style={styles.attachButton}
          onPress={handleAttachmentPress}
        >
          <Ionicons name="add" size={24} color={theme.PRIMARY} />
        </TouchableOpacity>
        
        <TextInput
          style={[
            styles.textInput,
            { 
              backgroundColor: theme.INPUT_BACKGROUND,
              color: theme.TEXT_PRIMARY 
            }
          ]}
          placeholder="Type a message..."
          placeholderTextColor={theme.TEXT_PLACEHOLDER}
          value={message}
          onChangeText={handleTyping}
          multiline
          maxLength={1000}
        />
        
        <TouchableOpacity
          style={[
            styles.sendButton,
            { backgroundColor: message.trim() ? theme.PRIMARY : theme.TEXT_PLACEHOLDER }
          ]}
          onPress={handleSendMessage}
          disabled={!message.trim() || sendMessageMutation.isPending}
        >
          <Ionicons name="send" size={20} color="#fff" />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  messagesList: {
    flex: 1,
  },
  messagesContainer: {
    padding: 16,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'flex-end',
  },
  ownMessageContainer: {
    justifyContent: 'flex-end',
  },
  otherMessageContainer: {
    justifyContent: 'flex-start',
  },
  messageAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
  },
  messageBubble: {
    maxWidth: width * 0.75,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
  },
  ownMessageBubble: {
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    borderBottomLeftRadius: 4,
  },
  messageBubbleNoAvatar: {
    marginLeft: 38,
  },
  replyContainer: {
    borderLeftWidth: 3,
    paddingLeft: 8,
    marginBottom: 8,
  },
  replyText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  messageImage: {
    width: 200,
    height: 200,
    borderRadius: 12,
    marginBottom: 4,
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  fileName: {
    marginLeft: 8,
    fontSize: 14,
  },
  messageTime: {
    fontSize: 10,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  typingText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  replyBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  replyInfo: {
    flex: 1,
  },
  replyLabel: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  replyPreview: {
    fontSize: 14,
    marginTop: 2,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  attachButton: {
    padding: 8,
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    maxHeight: 100,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 24,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default ChatRoom;
