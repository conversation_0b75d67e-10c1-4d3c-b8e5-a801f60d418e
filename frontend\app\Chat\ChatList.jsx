import React, { useState, useContext, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  RefreshControl,
  TextInput,
  Alert,
} from 'react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import { 
  getChatRooms, 
  initializeSocket, 
  setupSocketListeners, 
  removeSocketListeners,
  markAllMessagesAsRead 
} from '../../api/chat/chatApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';

const ChatList = () => {
  const { theme } = useContext(ThemeContext);
  const { user } = useContext(AuthContext);
  const router = useRouter();
  const queryClient = useQueryClient();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState(new Set());
  const [typingUsers, setTypingUsers] = useState(new Map());

  // Fetch chat rooms
  const {
    data: chatRooms = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['chatRooms', user?.id],
    queryFn: () => getChatRooms(user.id),
    enabled: !!user?.id,
  });

  // Initialize socket connection
  useEffect(() => {
    if (!user?.id) return;

    const socket = initializeSocket(user.id);
    
    const socketCallbacks = {
      onMessage: (message) => {
        // Update chat rooms list with new message
        queryClient.setQueryData(['chatRooms', user.id], (oldData) => {
          if (!oldData) return oldData;
          
          return oldData.map(room => {
            if (room.id === message.roomId) {
              return {
                ...room,
                lastMessage: message,
                unreadCount: room.unreadCount + 1,
                updatedAt: message.createdAt,
              };
            }
            return room;
          });
        });
      },
      
      onMessageRead: ({ roomId, messageId, userId }) => {
        if (userId === user.id) return; // Don't update for own read receipts
        
        queryClient.setQueryData(['chatRooms', user.id], (oldData) => {
          if (!oldData) return oldData;
          
          return oldData.map(room => {
            if (room.id === roomId) {
              return {
                ...room,
                unreadCount: Math.max(0, room.unreadCount - 1),
              };
            }
            return room;
          });
        });
      },
      
      onUserOnline: (userId) => {
        setOnlineUsers(prev => new Set([...prev, userId]));
      },
      
      onUserOffline: (userId) => {
        setOnlineUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(userId);
          return newSet;
        });
      },
      
      onUserTyping: ({ roomId, userId, userName }) => {
        if (userId === user.id) return;
        
        setTypingUsers(prev => {
          const newMap = new Map(prev);
          newMap.set(roomId, { userId, userName });
          return newMap;
        });
        
        // Clear typing indicator after 3 seconds
        setTimeout(() => {
          setTypingUsers(prev => {
            const newMap = new Map(prev);
            newMap.delete(roomId);
            return newMap;
          });
        }, 3000);
      },
      
      onUserStoppedTyping: ({ roomId }) => {
        setTypingUsers(prev => {
          const newMap = new Map(prev);
          newMap.delete(roomId);
          return newMap;
        });
      },
      
      onError: (error) => {
        console.error('Socket error:', error);
        showToast('error', 'Connection Error', 'Failed to connect to chat server');
      },
      
      onConnectError: (error) => {
        console.error('Socket connection error:', error);
        showToast('error', 'Connection Error', 'Failed to connect to chat server');
      },
    };
    
    setupSocketListeners(socketCallbacks);
    
    return () => {
      removeSocketListeners();
    };
  }, [user?.id, queryClient]);

  // Refresh chat rooms when screen is focused
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const handleChatPress = useCallback((chatRoom) => {
    // Mark messages as read
    if (chatRoom.unreadCount > 0) {
      markAllMessagesAsRead(chatRoom.id).catch(console.error);
    }
    
    router.push({
      pathname: '/Chat/ChatRoom',
      params: { 
        roomId: chatRoom.id,
        roomName: chatRoom.name || getChatRoomName(chatRoom),
        roomType: chatRoom.type,
      },
    });
  }, [router]);

  const handleNewChatPress = useCallback(() => {
    Alert.alert(
      'New Chat',
      'Choose chat type',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Find Broker', 
          onPress: () => router.push('/Chat/FindBroker') 
        },
        { 
          text: 'Find Contractor', 
          onPress: () => router.push('/Chat/FindContractor') 
        },
        { 
          text: 'Direct Message', 
          onPress: () => router.push('/Chat/FindUser') 
        },
      ]
    );
  }, [router]);

  const getChatRoomName = useCallback((room) => {
    if (room.name) return room.name;
    
    if (room.type === 'direct') {
      const otherParticipant = room.participants.find(p => p.id !== user.id);
      return otherParticipant?.name || 'Unknown User';
    }
    
    if (room.type === 'land_discussion') {
      return `Land Discussion - ${room.metadata?.landTitle || 'Property'}`;
    }
    
    if (room.type === 'contractor_group') {
      return `Project - ${room.metadata?.projectName || 'Construction'}`;
    }
    
    return 'Group Chat';
  }, [user?.id]);

  const getLastMessagePreview = useCallback((message) => {
    if (!message) return 'No messages yet';
    
    switch (message.type) {
      case 'image':
        return '📷 Image';
      case 'file':
        return '📎 File';
      case 'location':
        return '📍 Location';
      default:
        return message.content || 'Message';
    }
  }, []);

  const formatTime = useCallback((timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  }, []);

  const getParticipantAvatars = useCallback((room) => {
    if (room.type === 'direct') {
      const otherParticipant = room.participants.find(p => p.id !== user.id);
      return [otherParticipant?.avatar || 'https://via.placeholder.com/50'];
    }
    
    return room.participants
      .filter(p => p.id !== user.id)
      .slice(0, 3)
      .map(p => p.avatar || 'https://via.placeholder.com/50');
  }, [user?.id]);

  const isUserOnline = useCallback((userId) => {
    return onlineUsers.has(userId);
  }, [onlineUsers]);

  const filteredChatRooms = chatRooms.filter(room => {
    const roomName = getChatRoomName(room).toLowerCase();
    return roomName.includes(searchQuery.toLowerCase());
  });

  const renderChatItem = ({ item }) => {
    const avatars = getParticipantAvatars(item);
    const isOnline = item.type === 'direct' && 
      item.participants.some(p => p.id !== user.id && isUserOnline(p.id));
    const typingInfo = typingUsers.get(item.id);
    
    return (
      <TouchableOpacity
        style={[styles.chatItem, { backgroundColor: theme.CARD_BACKGROUND }]}
        onPress={() => handleChatPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.avatarContainer}>
          {avatars.length === 1 ? (
            <View style={styles.singleAvatarContainer}>
              <Image source={{ uri: avatars[0] }} style={styles.avatar} />
              {isOnline && <View style={styles.onlineIndicator} />}
            </View>
          ) : (
            <View style={styles.multipleAvatarContainer}>
              {avatars.map((avatar, index) => (
                <Image
                  key={index}
                  source={{ uri: avatar }}
                  style={[
                    styles.smallAvatar,
                    { 
                      marginLeft: index > 0 ? -10 : 0,
                      zIndex: avatars.length - index,
                    }
                  ]}
                />
              ))}
            </View>
          )}
        </View>
        
        <View style={styles.chatInfo}>
          <View style={styles.chatHeader}>
            <Text 
              style={[styles.chatName, { color: theme.TEXT_PRIMARY }]} 
              numberOfLines={1}
            >
              {getChatRoomName(item)}
            </Text>
            <Text style={[styles.timestamp, { color: theme.TEXT_SECONDARY }]}>
              {item.lastMessage && formatTime(item.lastMessage.createdAt)}
            </Text>
          </View>
          
          <View style={styles.messageRow}>
            <Text 
              style={[
                styles.lastMessage, 
                { color: typingInfo ? theme.PRIMARY : theme.TEXT_SECONDARY }
              ]} 
              numberOfLines={1}
            >
              {typingInfo 
                ? `${typingInfo.userName} is typing...`
                : getLastMessagePreview(item.lastMessage)
              }
            </Text>
            
            {item.unreadCount > 0 && (
              <View style={[styles.unreadBadge, { backgroundColor: theme.PRIMARY }]}>
                <Text style={styles.unreadCount}>
                  {item.unreadCount > 99 ? '99+' : item.unreadCount}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <BackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.TEXT_PRIMARY }]}>
            Failed to load chats
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
            onPress={refetch}
          >
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <BackButton color="#fff" />
        <Text style={styles.headerTitle}>Chats</Text>
        <TouchableOpacity onPress={handleNewChatPress}>
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </LinearGradient>
      
      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
        <Ionicons name="search" size={20} color={theme.TEXT_PLACEHOLDER} />
        <TextInput
          style={[styles.searchInput, { color: theme.TEXT_PRIMARY }]}
          placeholder="Search chats..."
          placeholderTextColor={theme.TEXT_PLACEHOLDER}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={theme.TEXT_PLACEHOLDER} />
          </TouchableOpacity>
        )}
      </View>
      
      <FlatList
        data={filteredChatRooms}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.PRIMARY]}
          />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="chatbubbles-outline" size={64} color={theme.TEXT_PLACEHOLDER} />
            <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
              No chats yet
            </Text>
            <TouchableOpacity
              style={[styles.startChatButton, { backgroundColor: theme.PRIMARY }]}
              onPress={handleNewChatPress}
            >
              <Text style={styles.startChatText}>Start a Chat</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  avatarContainer: {
    marginRight: 16,
  },
  singleAvatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#fff',
  },
  multipleAvatarContainer: {
    flexDirection: 'row',
    width: 60,
  },
  smallAvatar: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    borderWidth: 2,
    borderColor: '#fff',
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  timestamp: {
    fontSize: 12,
  },
  messageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: 14,
    flex: 1,
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  startChatButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  startChatText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ChatList;
