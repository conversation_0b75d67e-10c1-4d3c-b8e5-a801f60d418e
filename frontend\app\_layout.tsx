import { Stack } from 'expo-router';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '../context/AuthContext';
import { ThemeProvider } from '../context/ThemeContext';
import Toast from 'react-native-toast-message';
import ToastConfig from './Components/Shared/ToastConfig';
import { View } from 'react-native';

// Create a client
const queryClient = new QueryClient();

export default function RootLayout() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <View style={{ flex: 1 }}>
            <Stack screenOptions={{ headerShown: false }} />
            <Toast config={ToastConfig}/>
          </View>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}
