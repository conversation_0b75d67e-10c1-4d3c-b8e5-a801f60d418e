import { Stack } from 'expo-router';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '../context/AuthContext';
import { ThemeProvider } from '../context/ThemeContext';
import Toast from 'react-native-toast-message';
import ToastConfig from './Components/Shared/ToastConfig';
import { View, SafeAreaView } from 'react-native';

// Create a client
const queryClient = new QueryClient();

export default function RootLayout() {
    return (
        <SafeAreaView style={{ flex: 1 }}>
            <QueryClientProvider client={queryClient}>
                <ThemeProvider>
                    {/* <AuthProvider> */}
                    <Stack screenOptions={{ headerShown: false }} style={{ flex: 1 }}/>
                    <Toast config={ToastConfig} />
                    {/* </AuthProvider> */}
                </ThemeProvider>
            </QueryClientProvider>
        </SafeAreaView>
    );
}
