import React, {
    useState,
    useContext,
    useEffect,
    useCallback,
    useRef,
} from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    TextInput,
    Modal,
    ScrollView,
    Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import MapView, { Marker, Callout, Circle, Polygon } from 'react-native-maps';
import { useQuery, useMutation } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import {
    getCurrentLocation,
    getNearbyLands,
    getNearbyBrokers,
    getNearbyContractors,
    getNearbyPOIs,
    calculateDistance,
    getLocationAnalytics,
} from '../../api/map/mapApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';

const MapExplorer = () => {
    const { theme } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const router = useRouter();
    const mapRef = useRef(null);

    const [currentLocation, setCurrentLocation] = useState(null);
    const [mapRegion, setMapRegion] = useState({
        latitude: 28.6139, // Default to Delhi
        longitude: 77.209,
        latitudeDelta: 0.1,
        longitudeDelta: 0.1,
    });
    const [selectedMarker, setSelectedMarker] = useState(null);
    const [showFilters, setShowFilters] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [activeLayer, setActiveLayer] = useState('lands'); // 'lands', 'brokers', 'contractors', 'poi'
    const [radius, setRadius] = useState(10); // km
    const [showRadius, setShowRadius] = useState(false);

    // Get current location on mount
    useEffect(() => {
        getCurrentLocation()
            .then((location) => {
                setCurrentLocation(location);
                setMapRegion({
                    latitude: location.latitude,
                    longitude: location.longitude,
                    latitudeDelta: 0.05,
                    longitudeDelta: 0.05,
                });
            })
            .catch((error) => {
                console.error('Failed to get location:', error);
                showToast(
                    'error',
                    'Location Error',
                    'Failed to get your location'
                );
            });
    }, []);

    // Fetch nearby lands
    const { data: nearbyLands = [], refetch: refetchLands } = useQuery({
        queryKey: [
            'nearbyLands',
            mapRegion.latitude,
            mapRegion.longitude,
            radius,
        ],
        queryFn: () =>
            getNearbyLands(mapRegion.latitude, mapRegion.longitude, radius),
        enabled: activeLayer === 'lands',
    });

    // Fetch nearby brokers
    const { data: nearbyBrokers = [], refetch: refetchBrokers } = useQuery({
        queryKey: [
            'nearbyBrokers',
            mapRegion.latitude,
            mapRegion.longitude,
            radius,
        ],
        queryFn: () =>
            getNearbyBrokers(mapRegion.latitude, mapRegion.longitude, radius),
        enabled: activeLayer === 'brokers',
    });

    // Fetch nearby contractors
    const { data: nearbyContractors = [], refetch: refetchContractors } =
        useQuery({
            queryKey: [
                'nearbyContractors',
                mapRegion.latitude,
                mapRegion.longitude,
                radius,
            ],
            queryFn: () =>
                getNearbyContractors(
                    mapRegion.latitude,
                    mapRegion.longitude,
                    radius
                ),
            enabled: activeLayer === 'contractors',
        });

    // Fetch nearby POIs
    const { data: nearbyPOIs = [], refetch: refetchPOIs } = useQuery({
        queryKey: [
            'nearbyPOIs',
            mapRegion.latitude,
            mapRegion.longitude,
            radius,
        ],
        queryFn: () =>
            getNearbyPOIs(
                mapRegion.latitude,
                mapRegion.longitude,
                'all',
                radius
            ),
        enabled: activeLayer === 'poi',
    });

    const handleRegionChange = useCallback((region) => {
        setMapRegion(region);
    }, []);

    const handleMarkerPress = useCallback((marker) => {
        setSelectedMarker(marker);
    }, []);

    const handleMyLocationPress = useCallback(() => {
        if (currentLocation) {
            mapRef.current?.animateToRegion({
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
                latitudeDelta: 0.05,
                longitudeDelta: 0.05,
            });
        } else {
            getCurrentLocation()
                .then((location) => {
                    setCurrentLocation(location);
                    mapRef.current?.animateToRegion({
                        latitude: location.latitude,
                        longitude: location.longitude,
                        latitudeDelta: 0.05,
                        longitudeDelta: 0.05,
                    });
                })
                .catch((error) => {
                    showToast('error', 'Error', 'Failed to get your location');
                });
        }
    }, [currentLocation]);

    const handleLayerChange = useCallback((layer) => {
        setActiveLayer(layer);
        setSelectedMarker(null);
    }, []);

    const handleMarkerCalloutPress = useCallback(
        (marker) => {
            switch (activeLayer) {
                case 'lands':
                    router.push({
                        pathname: '/Properties/LandDetails',
                        params: { landId: marker.id },
                    });
                    break;
                case 'brokers':
                    router.push({
                        pathname: '/Brokers/BrokerProfile',
                        params: { brokerId: marker.id },
                    });
                    break;
                case 'contractors':
                    router.push({
                        pathname: '/Contractors/ContractorProfile',
                        params: { contractorId: marker.id },
                    });
                    break;
                default:
                    break;
            }
        },
        [activeLayer, router]
    );

    const getMarkerColor = useCallback(
        (type) => {
            switch (type) {
                case 'lands':
                    return theme.PRIMARY;
                case 'brokers':
                    return '#4CAF50';
                case 'contractors':
                    return '#FF9800';
                case 'poi':
                    return '#9C27B0';
                default:
                    return theme.PRIMARY;
            }
        },
        [theme]
    );

    const getMarkerIcon = useCallback((type, category) => {
        switch (type) {
            case 'lands':
                return 'location';
            case 'brokers':
                return 'person';
            case 'contractors':
                return 'hammer';
            case 'poi':
                switch (category) {
                    case 'school':
                        return 'school';
                    case 'hospital':
                        return 'medical';
                    case 'bank':
                        return 'card';
                    case 'transport':
                        return 'train';
                    default:
                        return 'business';
                }
            default:
                return 'location';
        }
    }, []);

    const formatDistance = useCallback((distance) => {
        if (distance < 1) {
            return `${Math.round(distance * 1000)}m`;
        }
        return `${distance.toFixed(1)}km`;
    }, []);

    const formatPrice = useCallback((price) => {
        if (price >= ********) {
            return `₹${(price / ********).toFixed(1)}Cr`;
        } else if (price >= 100000) {
            return `₹${(price / 100000).toFixed(1)}L`;
        }
        return `₹${price.toLocaleString()}`;
    }, []);

    const renderMarkers = useCallback(() => {
        let markers = [];

        switch (activeLayer) {
            case 'lands':
                markers = nearbyLands.map((land) => ({
                    ...land,
                    type: 'lands',
                    coordinate: {
                        latitude: land.coordinates.latitude,
                        longitude: land.coordinates.longitude,
                    },
                }));
                break;
            case 'brokers':
                markers = nearbyBrokers.map((broker) => ({
                    ...broker,
                    type: 'brokers',
                    coordinate: {
                        latitude: broker.location.latitude,
                        longitude: broker.location.longitude,
                    },
                }));
                break;
            case 'contractors':
                markers = nearbyContractors.map((contractor) => ({
                    ...contractor,
                    type: 'contractors',
                    coordinate: {
                        latitude: contractor.location.latitude,
                        longitude: contractor.location.longitude,
                    },
                }));
                break;
            case 'poi':
                markers = nearbyPOIs.map((poi) => ({
                    ...poi,
                    type: 'poi',
                    coordinate: {
                        latitude: poi.coordinates.latitude,
                        longitude: poi.coordinates.longitude,
                    },
                }));
                break;
        }

        return markers.map((marker, index) => {
            const distance = currentLocation
                ? calculateDistance(
                      currentLocation.latitude,
                      currentLocation.longitude,
                      marker.coordinate.latitude,
                      marker.coordinate.longitude
                  )
                : 0;

            return (
                <Marker
                    key={`${marker.type}-${marker.id}-${index}`}
                    coordinate={marker.coordinate}
                    pinColor={getMarkerColor(marker.type)}
                    onPress={() => handleMarkerPress(marker)}
                >
                    <Callout onPress={() => handleMarkerCalloutPress(marker)}>
                        <View style={styles.calloutContainer}>
                            <Text style={styles.calloutTitle} numberOfLines={1}>
                                {marker.title || marker.name}
                            </Text>
                            {marker.type === 'lands' && (
                                <Text style={styles.calloutPrice}>
                                    {formatPrice(marker.price)}
                                </Text>
                            )}
                            {marker.type === 'brokers' && (
                                <Text style={styles.calloutSubtitle}>
                                    {marker.specialization}
                                </Text>
                            )}
                            {marker.type === 'contractors' && (
                                <Text style={styles.calloutSubtitle}>
                                    {marker.services?.join(', ')}
                                </Text>
                            )}
                            <Text style={styles.calloutDistance}>
                                {formatDistance(distance)} away
                            </Text>
                        </View>
                    </Callout>
                </Marker>
            );
        });
    }, [
        activeLayer,
        nearbyLands,
        nearbyBrokers,
        nearbyContractors,
        nearbyPOIs,
        currentLocation,
        getMarkerColor,
        handleMarkerPress,
        handleMarkerCalloutPress,
        formatDistance,
        formatPrice,
    ]);

    const renderLayerButtons = () => (
        <View
            style={[
                styles.layerContainer,
                { backgroundColor: theme.CARD_BACKGROUND },
            ]}
        >
            {[
                { key: 'lands', label: 'Lands', icon: 'location' },
                { key: 'brokers', label: 'Brokers', icon: 'person' },
                { key: 'contractors', label: 'Contractors', icon: 'hammer' },
                { key: 'poi', label: 'POI', icon: 'business' },
            ].map((layer) => (
                <TouchableOpacity
                    key={layer.key}
                    style={[
                        styles.layerButton,
                        activeLayer === layer.key && {
                            backgroundColor: theme.PRIMARY,
                        },
                    ]}
                    onPress={() => handleLayerChange(layer.key)}
                >
                    <Ionicons
                        name={layer.icon}
                        size={16}
                        color={
                            activeLayer === layer.key
                                ? '#fff'
                                : theme.TEXT_SECONDARY
                        }
                    />
                    <Text
                        style={[
                            styles.layerButtonText,
                            {
                                color:
                                    activeLayer === layer.key
                                        ? '#fff'
                                        : theme.TEXT_SECONDARY,
                            },
                        ]}
                    >
                        {layer.label}
                    </Text>
                </TouchableOpacity>
            ))}
        </View>
    );

    const renderFilterModal = () => (
        <Modal
            visible={showFilters}
            animationType="slide"
            transparent
            onRequestClose={() => setShowFilters(false)}
        >
            <View style={styles.modalOverlay}>
                <View
                    style={[
                        styles.modalContent,
                        { backgroundColor: theme.CARD_BACKGROUND },
                    ]}
                >
                    <View style={styles.modalHeader}>
                        <Text
                            style={[
                                styles.modalTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Map Filters
                        </Text>
                        <TouchableOpacity onPress={() => setShowFilters(false)}>
                            <Ionicons
                                name="close"
                                size={24}
                                color={theme.TEXT_SECONDARY}
                            />
                        </TouchableOpacity>
                    </View>

                    <ScrollView style={styles.modalBody}>
                        <View style={styles.filterSection}>
                            <Text
                                style={[
                                    styles.filterLabel,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Search Radius: {radius}km
                            </Text>
                            <View style={styles.radiusContainer}>
                                {[5, 10, 20, 50].map((r) => (
                                    <TouchableOpacity
                                        key={r}
                                        style={[
                                            styles.radiusButton,
                                            radius === r && {
                                                backgroundColor: theme.PRIMARY,
                                            },
                                            { borderColor: theme.BORDER },
                                        ]}
                                        onPress={() => setRadius(r)}
                                    >
                                        <Text
                                            style={[
                                                styles.radiusButtonText,
                                                {
                                                    color:
                                                        radius === r
                                                            ? '#fff'
                                                            : theme.TEXT_PRIMARY,
                                                },
                                            ]}
                                        >
                                            {r}km
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </View>

                        <View style={styles.filterSection}>
                            <Text
                                style={[
                                    styles.filterLabel,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Show Search Radius
                            </Text>
                            <TouchableOpacity
                                style={[
                                    styles.toggleButton,
                                    {
                                        backgroundColor: showRadius
                                            ? theme.PRIMARY
                                            : theme.INPUT_BACKGROUND,
                                    },
                                ]}
                                onPress={() => setShowRadius(!showRadius)}
                            >
                                <Ionicons
                                    name={showRadius ? 'checkmark' : 'close'}
                                    size={20}
                                    color={
                                        showRadius
                                            ? '#fff'
                                            : theme.TEXT_SECONDARY
                                    }
                                />
                            </TouchableOpacity>
                        </View>
                    </ScrollView>
                </View>
            </View>
        </Modal>
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <BackButton color="#fff" />
                <Text style={styles.headerTitle}>Map Explorer</Text>
                <TouchableOpacity onPress={() => setShowFilters(true)}>
                    <Ionicons name="options" size={24} color="#fff" />
                </TouchableOpacity>
            </LinearGradient>

            {/* Search Bar */}
            <View
                style={[
                    styles.searchContainer,
                    { backgroundColor: theme.CARD_BACKGROUND },
                ]}
            >
                <Ionicons
                    name="search"
                    size={20}
                    color={theme.TEXT_PLACEHOLDER}
                />
                <TextInput
                    style={[styles.searchInput, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Search location..."
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                />
            </View>

            {/* Map */}
            <View style={styles.mapContainer}>
                <MapView
                    ref={mapRef}
                    style={styles.map}
                    region={mapRegion}
                    onRegionChangeComplete={handleRegionChange}
                    showsUserLocation={true}
                    showsMyLocationButton={false}
                    showsCompass={true}
                    showsScale={true}
                >
                    {/* Current location marker */}
                    {currentLocation && (
                        <Marker
                            coordinate={currentLocation}
                            title="Your Location"
                            pinColor="#4CAF50"
                        />
                    )}

                    {/* Search radius circle */}
                    {showRadius && currentLocation && (
                        <Circle
                            center={currentLocation}
                            radius={radius * 1000} // Convert km to meters
                            strokeColor={theme.PRIMARY}
                            strokeWidth={2}
                            fillColor={`${theme.PRIMARY}20`}
                        />
                    )}

                    {/* Dynamic markers */}
                    {renderMarkers()}
                </MapView>

                {/* My Location Button */}
                <TouchableOpacity
                    style={[
                        styles.myLocationButton,
                        { backgroundColor: theme.CARD_BACKGROUND },
                    ]}
                    onPress={handleMyLocationPress}
                >
                    <Ionicons name="locate" size={24} color={theme.PRIMARY} />
                </TouchableOpacity>
            </View>

            {/* Layer Selection */}
            {renderLayerButtons()}

            {/* Filter Modal */}
            {renderFilterModal()}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingTop: 50,
        paddingBottom: 20,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        margin: 16,
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    searchInput: {
        flex: 1,
        marginLeft: 12,
        fontSize: 16,
    },
    mapContainer: {
        flex: 1,
        position: 'relative',
    },
    map: {
        flex: 1,
    },
    myLocationButton: {
        position: 'absolute',
        bottom: 100,
        right: 16,
        width: 50,
        height: 50,
        borderRadius: 25,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
    layerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: 12,
        paddingHorizontal: 16,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    layerButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
    },
    layerButtonText: {
        marginLeft: 4,
        fontSize: 12,
        fontWeight: '600',
    },
    calloutContainer: {
        minWidth: 150,
        padding: 8,
    },
    calloutTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        marginBottom: 2,
    },
    calloutPrice: {
        fontSize: 12,
        color: '#4CAF50',
        fontWeight: '600',
    },
    calloutSubtitle: {
        fontSize: 12,
        color: '#666',
        marginBottom: 2,
    },
    calloutDistance: {
        fontSize: 10,
        color: '#999',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'flex-end',
    },
    modalContent: {
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        maxHeight: '70%',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    modalBody: {
        padding: 20,
    },
    filterSection: {
        marginBottom: 24,
    },
    filterLabel: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 12,
    },
    radiusContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    radiusButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
    },
    radiusButtonText: {
        fontSize: 14,
        fontWeight: '600',
    },
    toggleButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default MapExplorer;
