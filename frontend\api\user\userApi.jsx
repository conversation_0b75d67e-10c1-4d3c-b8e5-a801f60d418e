import { privateAPIClient } from '../index';

export const fetchUserProfile = async () => {
    const response = await privateAPIClient.get(
        '/user-service/api/v1/user/profile'
    );
    return response.data.user;
};

export const updateUserProfile = async (data) => {
    const response = await privateAPIClient.put(
        '/user-service/api/v1/user/profile',
        data
    );
    return response.data;
};
