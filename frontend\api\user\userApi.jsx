import { privateAPIClient } from '../index';

export const fetchUserProfile = async () => {
    const response = await privateAPIClient.get(
        '/user-service/api/v1/user/profile'
    );
    return response.data.user;
};

export const updateUserProfile = async (data) => {
    // Check if data contains an image file
    const hasImageFile = data.avatar && typeof data.avatar === 'object' && data.avatar.uri;

    if (hasImageFile) {
        // Create FormData for multipart/form-data request
        const formData = new FormData();

        // Append the image file
        formData.append('avatar', {
            uri: data.avatar.uri,
            type: data.avatar.type || 'image/jpeg',
            name: data.avatar.name || 'avatar.jpg',
        });

        // Append other fields
        Object.keys(data).forEach(key => {
            if (key !== 'avatar' && data[key] !== undefined && data[key] !== null) {
                formData.append(key, data[key]);
            }
        });

        const response = await privateAPIClient.put(
            '/user-service/api/v1/user/profile',
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            }
        );
        return response.data;
    } else {
        // Regular JSON request for text-only updates
        const response = await privateAPIClient.put(
            '/user-service/api/v1/user/profile',
            data,
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
        return response.data;
    }
};