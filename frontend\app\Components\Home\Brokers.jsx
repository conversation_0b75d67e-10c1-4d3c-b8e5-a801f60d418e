import React, { useContext } from 'react';
import { Text } from 'react-native'; // <-- Add this import
import { Linking } from 'react-native';
import CategorySection from '../Shared/CategorySection';
import { ThemeContext } from '../../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { fetchBrokerProfile } from '../../../api/broker/brokerApi';

export default function Brokers() {
    const {data:broker, isLoading, isError, error} = useQuery({
        queryKey: ['brokerProfile'],
        queryFn: fetchBrokerProfile,
    });

    return (
        isLoading ? (
            <Text>Loading...</Text>
        ) : isError ? (
            <Text>Error: {error.message}</Text>
        ) : (
            <CategorySection 
                title="Featured Brokers"
                role="broker"
                data={broker}  
                onItemPress={() => {}}
                viewAllRoute="/Brokers/BrokerList"
            />
        )
    )
}
