import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Switch,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';

const Settings = () => {
  const { theme, toggleTheme } = useContext(ThemeContext);
  const { logout } = useContext(AuthContext);
  const router = useRouter();

  const [settings, setSettings] = useState({
    notifications: true,
    emailNotifications: true,
    pushNotifications: true,
    locationServices: true,
    biometricAuth: false,
    autoBackup: true,
    dataSync: true,
    darkMode: false,
  });

  const toggleSetting = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            logout();
            router.replace('/auth/Login');
          },
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Handle account deletion
            Alert.alert('Account Deleted', 'Your account has been deleted successfully.');
          },
        },
      ]
    );
  };

  const settingsGroups = [
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          title: 'Edit Profile',
          icon: 'person',
          onPress: () => router.push('/Profile/EditProfile'),
          showArrow: true,
        },
        {
          id: 'security',
          title: 'Security & Privacy',
          icon: 'shield-checkmark',
          onPress: () => router.push('/Settings/SecuritySettings'),
          showArrow: true,
        },
        {
          id: 'biometric',
          title: 'Biometric Authentication',
          icon: 'finger-print',
          type: 'switch',
          value: settings.biometricAuth,
          onToggle: () => toggleSetting('biometricAuth'),
        },
      ],
    },
    {
      title: 'Notifications',
      items: [
        {
          id: 'notifications',
          title: 'Push Notifications',
          icon: 'notifications',
          type: 'switch',
          value: settings.pushNotifications,
          onToggle: () => toggleSetting('pushNotifications'),
        },
        {
          id: 'email',
          title: 'Email Notifications',
          icon: 'mail',
          type: 'switch',
          value: settings.emailNotifications,
          onToggle: () => toggleSetting('emailNotifications'),
        },
        {
          id: 'notification-settings',
          title: 'Notification Preferences',
          icon: 'settings',
          onPress: () => router.push('/Settings/NotificationSettings'),
          showArrow: true,
        },
      ],
    },
    {
      title: 'App Preferences',
      items: [
        {
          id: 'theme',
          title: 'Dark Mode',
          icon: 'moon',
          type: 'switch',
          value: settings.darkMode,
          onToggle: () => {
            toggleSetting('darkMode');
            toggleTheme();
          },
        },
        {
          id: 'location',
          title: 'Location Services',
          icon: 'location',
          type: 'switch',
          value: settings.locationServices,
          onToggle: () => toggleSetting('locationServices'),
        },
        {
          id: 'language',
          title: 'Language',
          icon: 'language',
          subtitle: 'English',
          onPress: () => router.push('/Settings/LanguageSettings'),
          showArrow: true,
        },
      ],
    },
    {
      title: 'Data & Storage',
      items: [
        {
          id: 'backup',
          title: 'Auto Backup',
          icon: 'cloud-upload',
          type: 'switch',
          value: settings.autoBackup,
          onToggle: () => toggleSetting('autoBackup'),
        },
        {
          id: 'sync',
          title: 'Data Sync',
          icon: 'sync',
          type: 'switch',
          value: settings.dataSync,
          onToggle: () => toggleSetting('dataSync'),
        },
        {
          id: 'storage',
          title: 'Storage Management',
          icon: 'folder',
          subtitle: '2.3 GB used',
          onPress: () => router.push('/Settings/StorageSettings'),
          showArrow: true,
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          title: 'Help & Support',
          icon: 'help-circle',
          onPress: () => router.push('/Support/HelpSupport'),
          showArrow: true,
        },
        {
          id: 'feedback',
          title: 'Send Feedback',
          icon: 'chatbubble-ellipses',
          onPress: () => router.push('/Support/Feedback'),
          showArrow: true,
        },
        {
          id: 'about',
          title: 'About',
          icon: 'information-circle',
          onPress: () => router.push('/About/About'),
          showArrow: true,
        },
      ],
    },
    {
      title: 'Account Actions',
      items: [
        {
          id: 'logout',
          title: 'Logout',
          icon: 'log-out',
          color: '#FF9800',
          onPress: handleLogout,
        },
        {
          id: 'delete',
          title: 'Delete Account',
          icon: 'trash',
          color: '#F44336',
          onPress: handleDeleteAccount,
        },
      ],
    },
  ];

  const renderSettingItem = (item) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.settingItem, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={item.onPress}
      disabled={item.type === 'switch'}
    >
      <View style={styles.settingLeft}>
        <View style={[
          styles.settingIcon,
          { backgroundColor: (item.color || theme.PRIMARY) + '20' }
        ]}>
          <Ionicons
            name={item.icon}
            size={20}
            color={item.color || theme.PRIMARY}
          />
        </View>
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>
            {item.title}
          </Text>
          {item.subtitle && (
            <Text style={[styles.settingSubtitle, { color: theme.TEXT_SECONDARY }]}>
              {item.subtitle}
            </Text>
          )}
        </View>
      </View>

      <View style={styles.settingRight}>
        {item.type === 'switch' ? (
          <Switch
            value={item.value}
            onValueChange={item.onToggle}
            trackColor={{ false: theme.TEXT_SECONDARY + '30', true: theme.PRIMARY + '50' }}
            thumbColor={item.value ? theme.PRIMARY : '#f4f3f4'}
          />
        ) : item.showArrow ? (
          <Ionicons name="chevron-forward" size={20} color={theme.TEXT_SECONDARY} />
        ) : null}
      </View>
    </TouchableOpacity>
  );

  const renderSettingGroup = (group) => (
    <View key={group.title} style={styles.settingGroup}>
      <Text style={[styles.groupTitle, { color: theme.TEXT_PRIMARY }]}>
        {group.title}
      </Text>
      <View style={styles.groupItems}>
        {group.items.map(renderSettingItem)}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Settings</Text>
          <View style={{ width: 24 }} />
        </View>
      </LinearGradient>

      {/* Settings List */}
      <ScrollView
        style={styles.settingsList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.settingsContent}
      >
        {settingsGroups.map(renderSettingGroup)}
        
        {/* App Version */}
        <View style={styles.versionContainer}>
          <Text style={[styles.versionText, { color: theme.TEXT_SECONDARY }]}>
            BuildConnect v1.0.0
          </Text>
          <Text style={[styles.buildText, { color: theme.TEXT_SECONDARY }]}>
            Build 2024.01.15
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  settingsList: {
    flex: 1,
  },
  settingsContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  settingGroup: {
    marginBottom: 32,
  },
  groupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    marginLeft: 4,
  },
  groupItems: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  settingSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  settingRight: {
    marginLeft: 12,
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  versionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  buildText: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default Settings;
