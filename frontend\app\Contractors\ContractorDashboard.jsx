import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

const ContractorDashboard = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Mock contractor data
  const contractorStats = {
    activeProjects: 8,
    completedProjects: 45,
    totalEarnings: 1250000,
    monthlyEarnings: 185000,
    rating: 4.7,
    reviews: 89,
    clients: 67,
    pendingPayments: 45000,
  };

  const activeProjects = [
    {
      id: 1,
      title: 'Villa Construction - Whitefield',
      client: '<PERSON><PERSON>',
      progress: 75,
      deadline: '2024-03-15',
      budget: '₹25 Lakh',
      status: 'on-track',
    },
    {
      id: 2,
      title: 'Office Interior - Electronic City',
      client: 'Tech Solutions Pvt Ltd',
      progress: 45,
      deadline: '2024-02-28',
      budget: '₹8 Lakh',
      status: 'delayed',
    },
    {
      id: 3,
      title: 'Apartment Renovation - HSR Layout',
      client: 'Priya Sharma',
      progress: 90,
      deadline: '2024-02-10',
      budget: '₹12 Lakh',
      status: 'on-track',
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'project',
      title: 'Project milestone completed',
      description: 'Foundation work completed for Villa project',
      time: '2 hours ago',
      icon: 'checkmark-circle',
      color: '#4CAF50',
    },
    {
      id: 2,
      type: 'payment',
      title: 'Payment received',
      description: '₹50,000 received from client',
      time: '5 hours ago',
      icon: 'wallet',
      color: '#2196F3',
    },
    {
      id: 3,
      type: 'inquiry',
      title: 'New project inquiry',
      description: 'Home renovation project in Koramangala',
      time: '1 day ago',
      icon: 'mail',
      color: '#FF9800',
    },
    {
      id: 4,
      type: 'review',
      title: 'New review received',
      description: '5-star rating for completed project',
      time: '2 days ago',
      icon: 'star',
      color: '#9C27B0',
    },
  ];

  const quickActions = [
    {
      id: 'add-project',
      title: 'Add Project',
      icon: 'add-circle',
      color: '#4CAF50',
      onPress: () => router.push('/Contractors/AddProject'),
    },
    {
      id: 'manage-projects',
      title: 'Manage Projects',
      icon: 'briefcase',
      color: '#2196F3',
      onPress: () => router.push('/Contractors/ManageProjects'),
    },
    {
      id: 'invoices',
      title: 'Invoices',
      icon: 'receipt',
      color: '#FF9800',
      onPress: () => router.push('/Contractors/Invoices'),
    },
    {
      id: 'materials',
      title: 'Materials',
      icon: 'cube',
      color: '#9C27B0',
      onPress: () => router.push('/Contractors/Materials'),
    },
  ];

  const periods = [
    { id: 'week', name: 'This Week' },
    { id: 'month', name: 'This Month' },
    { id: 'quarter', name: 'This Quarter' },
    { id: 'year', name: 'This Year' },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'on-track': return '#4CAF50';
      case 'delayed': return '#F44336';
      case 'completed': return '#2196F3';
      default: return '#FF9800';
    }
  };

  const renderStatCard = (title, value, subtitle, icon, color) => (
    <View style={[styles.statCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
      <View style={styles.statHeader}>
        <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
          <Ionicons name={icon} size={24} color={color} />
        </View>
        <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>
          {value}
        </Text>
      </View>
      <Text style={[styles.statTitle, { color: theme.TEXT_SECONDARY }]}>
        {title}
      </Text>
      {subtitle && (
        <Text style={[styles.statSubtitle, { color: theme.TEXT_SECONDARY }]}>
          {subtitle}
        </Text>
      )}
    </View>
  );

  const renderProject = (project) => (
    <TouchableOpacity
      key={project.id}
      style={[styles.projectCard, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={() => router.push(`/Contractors/ProjectDetails?id=${project.id}`)}
    >
      <View style={styles.projectHeader}>
        <Text style={[styles.projectTitle, { color: theme.TEXT_PRIMARY }]}>
          {project.title}
        </Text>
        <View style={[
          styles.statusBadge,
          { backgroundColor: getStatusColor(project.status) + '20' }
        ]}>
          <Text style={[styles.statusText, { color: getStatusColor(project.status) }]}>
            {project.status.replace('-', ' ')}
          </Text>
        </View>
      </View>
      
      <Text style={[styles.clientName, { color: theme.TEXT_SECONDARY }]}>
        Client: {project.client}
      </Text>
      
      <View style={styles.projectDetails}>
        <Text style={[styles.projectBudget, { color: theme.PRIMARY }]}>
          {project.budget}
        </Text>
        <Text style={[styles.projectDeadline, { color: theme.TEXT_SECONDARY }]}>
          Due: {new Date(project.deadline).toLocaleDateString()}
        </Text>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={[styles.progressLabel, { color: theme.TEXT_SECONDARY }]}>
            Progress
          </Text>
          <Text style={[styles.progressValue, { color: theme.TEXT_PRIMARY }]}>
            {project.progress}%
          </Text>
        </View>
        <View style={[styles.progressBar, { backgroundColor: theme.BACKGROUND }]}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${project.progress}%`,
                backgroundColor: getStatusColor(project.status),
              }
            ]}
          />
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderActivity = (activity) => (
    <TouchableOpacity
      key={activity.id}
      style={[styles.activityCard, { backgroundColor: theme.CARD_BACKGROUND }]}
    >
      <View style={[styles.activityIcon, { backgroundColor: activity.color + '20' }]}>
        <Ionicons name={activity.icon} size={20} color={activity.color} />
      </View>
      <View style={styles.activityContent}>
        <Text style={[styles.activityTitle, { color: theme.TEXT_PRIMARY }]}>
          {activity.title}
        </Text>
        <Text style={[styles.activityDescription, { color: theme.TEXT_SECONDARY }]}>
          {activity.description}
        </Text>
        <Text style={[styles.activityTime, { color: theme.TEXT_SECONDARY }]}>
          {activity.time}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderQuickAction = (action) => (
    <TouchableOpacity
      key={action.id}
      style={[styles.actionCard, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={action.onPress}
    >
      <View style={[styles.actionIcon, { backgroundColor: action.color + '20' }]}>
        <Ionicons name={action.icon} size={28} color={action.color} />
      </View>
      <Text style={[styles.actionTitle, { color: theme.TEXT_PRIMARY }]}>
        {action.title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Contractor Dashboard</Text>
          <TouchableOpacity onPress={() => router.push('/Contractors/ContractorProfile')}>
            <Ionicons name="person-circle" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Period Selector */}
        <View style={styles.periodSection}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.periodContainer}
          >
            {periods.map((period) => (
              <TouchableOpacity
                key={period.id}
                style={[
                  styles.periodButton,
                  {
                    backgroundColor: selectedPeriod === period.id ? theme.PRIMARY : theme.CARD_BACKGROUND,
                  }
                ]}
                onPress={() => setSelectedPeriod(period.id)}
              >
                <Text style={[
                  styles.periodText,
                  {
                    color: selectedPeriod === period.id ? '#fff' : theme.TEXT_SECONDARY,
                  }
                ]}>
                  {period.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Stats Overview */}
        <View style={styles.statsSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Overview
          </Text>
          <View style={styles.statsGrid}>
            {renderStatCard('Active Projects', contractorStats.activeProjects, `${contractorStats.completedProjects} completed`, 'briefcase', '#4CAF50')}
            {renderStatCard('Monthly Earnings', `₹${(contractorStats.monthlyEarnings / 1000).toFixed(0)}K`, `₹${(contractorStats.totalEarnings / 100000).toFixed(1)}L total`, 'wallet', '#2196F3')}
            {renderStatCard('Rating', contractorStats.rating, `${contractorStats.reviews} reviews`, 'star', '#FF9800')}
            {renderStatCard('Pending Payments', `₹${(contractorStats.pendingPayments / 1000).toFixed(0)}K`, 'to be received', 'time', '#9C27B0')}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Quick Actions
          </Text>
          <View style={styles.actionsGrid}>
            {quickActions.map(renderQuickAction)}
          </View>
        </View>

        {/* Active Projects */}
        <View style={styles.projectsSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Active Projects
            </Text>
            <TouchableOpacity onPress={() => router.push('/Contractors/AllProjects')}>
              <Text style={[styles.viewAllText, { color: theme.PRIMARY }]}>
                View All
              </Text>
            </TouchableOpacity>
          </View>
          <View style={styles.projectsList}>
            {activeProjects.map(renderProject)}
          </View>
        </View>

        {/* Recent Activities */}
        <View style={styles.activitiesSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Recent Activities
          </Text>
          <View style={styles.activitiesList}>
            {recentActivities.map(renderActivity)}
          </View>
        </View>

        <View style={{ height: 40 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  periodSection: {
    paddingVertical: 16,
  },
  periodContainer: {
    paddingHorizontal: 20,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  periodText: {
    fontSize: 14,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statsSection: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  statCard: {
    width: (width - 60) / 2,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  statSubtitle: {
    fontSize: 12,
  },
  actionsSection: {
    marginBottom: 24,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (width - 60) / 2,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  projectsSection: {
    marginBottom: 24,
  },
  projectsList: {
    paddingHorizontal: 20,
  },
  projectCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  projectTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  clientName: {
    fontSize: 14,
    marginBottom: 8,
  },
  projectDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  projectBudget: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  projectDeadline: {
    fontSize: 14,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 12,
  },
  progressValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  activitiesSection: {
    marginBottom: 24,
  },
  activitiesList: {
    paddingHorizontal: 20,
  },
  activityCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  activityDescription: {
    fontSize: 12,
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 11,
  },
});

export default ContractorDashboard;
