// UI Components Library for BuildConnect
// Centralized export for all reusable UI components

// Buttons
export { default as PrimaryButton } from './Buttons/PrimaryButton';
export { default as SecondaryButton } from './Buttons/SecondaryButton';
export { default as IconButton } from './Buttons/IconButton';
export { default as FloatingActionButton } from './Buttons/FloatingActionButton';

// Cards
export { default as PropertyCard } from './Cards/PropertyCard';
export { default as ContractorCard } from './Cards/ContractorCard';
export { default as ServiceCard } from './Cards/ServiceCard';
export { default as NotificationCard } from './Cards/NotificationCard';
export { default as StatsCard } from './Cards/StatsCard';

// Forms
export { default as TextInput } from './Forms/TextInput';
export { default as SearchInput } from './Forms/SearchInput';
export { default as PasswordInput } from './Forms/PasswordInput';
export { default as PhoneInput } from './Forms/PhoneInput';
export { default as DatePicker } from './Forms/DatePicker';
export { default as ImagePicker } from './Forms/ImagePicker';
export { default as DocumentPicker } from './Forms/DocumentPicker';
export { default as LocationPicker } from './Forms/LocationPicker';
export { default as Dropdown } from './Forms/Dropdown';
export { default as Checkbox } from './Forms/Checkbox';
export { default as RadioButton } from './Forms/RadioButton';
export { default as Slider } from './Forms/Slider';
export { default as Switch } from './Forms/Switch';

// Layout
export { default as Container } from './Layout/Container';
export { default as Section } from './Layout/Section';
export { default as Row } from './Layout/Row';
export { default as Column } from './Layout/Column';
export { default as Spacer } from './Layout/Spacer';
export { default as Divider } from './Layout/Divider';
export { default as SafeAreaContainer } from './Layout/SafeAreaContainer';

// Navigation
export { default as Header } from './Navigation/Header';
export { default as BackButton } from './Navigation/BackButton';
export { default as TabBar } from './Navigation/TabBar';
export { default as Breadcrumb } from './Navigation/Breadcrumb';

// Feedback
export { default as LoadingSpinner } from './Feedback/LoadingSpinner';
export { default as LoadingOverlay } from './Feedback/LoadingOverlay';
export { default as EmptyState } from './Feedback/EmptyState';
export { default as ErrorState } from './Feedback/ErrorState';
export { default as SuccessMessage } from './Feedback/SuccessMessage';
export { default as Toast } from './Feedback/Toast';
export { default as ProgressBar } from './Feedback/ProgressBar';
export { default as Skeleton } from './Feedback/Skeleton';

// Media
export { default as Avatar } from './Media/Avatar';
export { default as ImageCarousel } from './Media/ImageCarousel';
export { default as VideoPlayer } from './Media/VideoPlayer';
export { default as ImageViewer } from './Media/ImageViewer';
export { default as FilePreview } from './Media/FilePreview';

// Lists
export { default as ListItem } from './Lists/ListItem';
export { default as SectionList } from './Lists/SectionList';
export { default as InfiniteList } from './Lists/InfiniteList';
export { default as SwipeableListItem } from './Lists/SwipeableListItem';

// Modals
export { default as Modal } from './Modals/Modal';
export { default as BottomSheet } from './Modals/BottomSheet';
export { default as ActionSheet } from './Modals/ActionSheet';
export { default as AlertDialog } from './Modals/AlertDialog';
export { default as ConfirmDialog } from './Modals/ConfirmDialog';

// Charts
export { default as LineChart } from './Charts/LineChart';
export { default as BarChart } from './Charts/BarChart';
export { default as PieChart } from './Charts/PieChart';
export { default as AreaChart } from './Charts/AreaChart';

// Maps
export { default as MapView } from './Maps/MapView';
export { default as MapMarker } from './Maps/MapMarker';
export { default as MapCluster } from './Maps/MapCluster';
export { default as MapSearch } from './Maps/MapSearch';

// Chat
export { default as MessageBubble } from './Chat/MessageBubble';
export { default as ChatInput } from './Chat/ChatInput';
export { default as TypingIndicator } from './Chat/TypingIndicator';
export { default as OnlineIndicator } from './Chat/OnlineIndicator';

// Filters
export { default as FilterChip } from './Filters/FilterChip';
export { default as FilterBar } from './Filters/FilterBar';
export { default as SortButton } from './Filters/SortButton';
export { default as SearchFilter } from './Filters/SearchFilter';

// Payment
export { default as PaymentCard } from './Payment/PaymentCard';
export { default as PaymentMethod } from './Payment/PaymentMethod';
export { default as PriceDisplay } from './Payment/PriceDisplay';
export { default as WalletBalance } from './Payment/WalletBalance';

// AI
export { default as AIBadge } from './AI/AIBadge';
export { default as RecommendationCard } from './AI/RecommendationCard';
export { default as ValidationStatus } from './AI/ValidationStatus';
export { default as ConfidenceScore } from './AI/ConfidenceScore';

// Utilities
export { default as Badge } from './Utilities/Badge';
export { default as Tag } from './Utilities/Tag';
export { default as Rating } from './Utilities/Rating';
export { default as StatusIndicator } from './Utilities/StatusIndicator';
export { default as Tooltip } from './Utilities/Tooltip';
export { default as Accordion } from './Utilities/Accordion';
export { default as Stepper } from './Utilities/Stepper';
export { default as Timeline } from './Utilities/Timeline';

// Theme and styling utilities
export { default as ThemeProvider } from './Theme/ThemeProvider';
export { default as useTheme } from './Theme/useTheme';
export { default as styled } from './Theme/styled';

// Animation components
export { default as FadeIn } from './Animations/FadeIn';
export { default as SlideIn } from './Animations/SlideIn';
export { default as ScaleIn } from './Animations/ScaleIn';
export { default as Bounce } from './Animations/Bounce';

// Gesture components
export { default as SwipeGesture } from './Gestures/SwipeGesture';
export { default as PinchZoom } from './Gestures/PinchZoom';
export { default as DragDrop } from './Gestures/DragDrop';

// Accessibility components
export { default as AccessibleText } from './Accessibility/AccessibleText';
export { default as AccessibleButton } from './Accessibility/AccessibleButton';
export { default as ScreenReader } from './Accessibility/ScreenReader';

// Constants and utilities
export * from './constants/colors';
export * from './constants/typography';
export * from './constants/spacing';
export * from './constants/shadows';
export * from './utils/responsive';
export * from './utils/animations';
export * from './utils/accessibility';

// Hooks
export { default as useResponsive } from './hooks/useResponsive';
export { default as useAnimation } from './hooks/useAnimation';
export { default as useKeyboard } from './hooks/useKeyboard';
export { default as useOrientation } from './hooks/useOrientation';
export { default as useSafeArea } from './hooks/useSafeArea';

// Higher-order components
export { default as withTheme } from './hoc/withTheme';
export { default as withLoading } from './hoc/withLoading';
export { default as withError } from './hoc/withError';
export { default as withKeyboard } from './hoc/withKeyboard';

// Component compositions
export { default as PropertyListItem } from './Compositions/PropertyListItem';
export { default as ContractorListItem } from './Compositions/ContractorListItem';
export { default as ChatListItem } from './Compositions/ChatListItem';
export { default as NotificationListItem } from './Compositions/NotificationListItem';
export { default as TransactionListItem } from './Compositions/TransactionListItem';

// Form compositions
export { default as LoginForm } from './Forms/Compositions/LoginForm';
export { default as RegisterForm } from './Forms/Compositions/RegisterForm';
export { default as PropertyForm } from './Forms/Compositions/PropertyForm';
export { default as ContractorForm } from './Forms/Compositions/ContractorForm';
export { default as PaymentForm } from './Forms/Compositions/PaymentForm';

// Screen templates
export { default as ListScreenTemplate } from './Templates/ListScreenTemplate';
export { default as DetailScreenTemplate } from './Templates/DetailScreenTemplate';
export { default as FormScreenTemplate } from './Templates/FormScreenTemplate';
export { default as DashboardTemplate } from './Templates/DashboardTemplate';
export { default as ChatScreenTemplate } from './Templates/ChatScreenTemplate';

// Platform-specific components
export { default as PlatformView } from './Platform/PlatformView';
export { default as PlatformText } from './Platform/PlatformText';
export { default as PlatformButton } from './Platform/PlatformButton';

// Development tools (only in development)
if (__DEV__) {
  export { default as DevTools } from './DevTools/DevTools';
  export { default as ComponentPreview } from './DevTools/ComponentPreview';
  export { default as ThemePreview } from './DevTools/ThemePreview';
}

// Version and metadata
export const UI_VERSION = '1.0.0';
export const COMPONENT_COUNT = 100; // Approximate count of components

// Component registry for dynamic imports
export const COMPONENT_REGISTRY = {
  // Buttons
  PrimaryButton: () => import('./Buttons/PrimaryButton'),
  SecondaryButton: () => import('./Buttons/SecondaryButton'),
  IconButton: () => import('./Buttons/IconButton'),
  
  // Cards
  PropertyCard: () => import('./Cards/PropertyCard'),
  ContractorCard: () => import('./Cards/ContractorCard'),
  
  // Forms
  TextInput: () => import('./Forms/TextInput'),
  SearchInput: () => import('./Forms/SearchInput'),
  
  // Add more as needed...
};

// Utility function to get component dynamically
export const getComponent = async (componentName) => {
  if (COMPONENT_REGISTRY[componentName]) {
    const component = await COMPONENT_REGISTRY[componentName]();
    return component.default;
  }
  throw new Error(`Component ${componentName} not found in registry`);
};

// Theme configuration
export const DEFAULT_THEME = {
  colors: {
    primary: '#007AFF',
    secondary: '#5856D6',
    success: '#34C759',
    warning: '#FF9500',
    error: '#FF3B30',
    background: '#F2F2F7',
    surface: '#FFFFFF',
    text: '#000000',
    textSecondary: '#8E8E93',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  typography: {
    h1: { fontSize: 32, fontWeight: 'bold' },
    h2: { fontSize: 24, fontWeight: 'bold' },
    h3: { fontSize: 20, fontWeight: '600' },
    body: { fontSize: 16, fontWeight: 'normal' },
    caption: { fontSize: 12, fontWeight: 'normal' },
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 8,
    },
  },
};

// Export everything as default for convenience
export default {
  // All named exports
  UI_VERSION,
  COMPONENT_COUNT,
  COMPONENT_REGISTRY,
  DEFAULT_THEME,
  getComponent,
};
