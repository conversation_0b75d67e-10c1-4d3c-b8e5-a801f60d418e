import React, { useState, useContext, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import {
  getDashboardStats,
  getRevenueAnalytics,
  getUserGrowthAnalytics,
  getTransactionAnalytics,
  getBrokerApplications,
  getContractorApplications,
  getLandVerificationQueue,
  getAllTickets,
} from '../../api/admin/adminApi';
import BackButton from '../Components/Shared/BackButton';

const { width } = Dimensions.get('window');

const AdminDashboard = () => {
  const { theme } = useContext(ThemeContext);
  const { user } = useContext(AuthContext);
  const router = useRouter();
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');

  // Fetch dashboard stats
  const {
    data: dashboardStats = {},
    isLoading: statsLoading,
    refetch: refetchStats,
  } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: getDashboardStats,
  });

  // Fetch revenue analytics
  const {
    data: revenueData = {},
    refetch: refetchRevenue,
  } = useQuery({
    queryKey: ['revenueAnalytics', selectedTimeframe],
    queryFn: () => getRevenueAnalytics(selectedTimeframe),
  });

  // Fetch user growth analytics
  const {
    data: userGrowthData = {},
    refetch: refetchUserGrowth,
  } = useQuery({
    queryKey: ['userGrowthAnalytics', selectedTimeframe],
    queryFn: () => getUserGrowthAnalytics(selectedTimeframe),
  });

  // Fetch pending applications
  const {
    data: brokerApplications = [],
    refetch: refetchBrokerApps,
  } = useQuery({
    queryKey: ['brokerApplications'],
    queryFn: () => getBrokerApplications('pending'),
  });

  const {
    data: contractorApplications = [],
    refetch: refetchContractorApps,
  } = useQuery({
    queryKey: ['contractorApplications'],
    queryFn: () => getContractorApplications('pending'),
  });

  // Fetch verification queue
  const {
    data: verificationQueue = [],
    refetch: refetchVerificationQueue,
  } = useQuery({
    queryKey: ['landVerificationQueue'],
    queryFn: getLandVerificationQueue,
  });

  // Fetch support tickets
  const {
    data: supportTickets = {},
    refetch: refetchTickets,
  } = useQuery({
    queryKey: ['supportTickets'],
    queryFn: () => getAllTickets(1, 10, { status: 'open' }),
  });

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([
      refetchStats(),
      refetchRevenue(),
      refetchUserGrowth(),
      refetchBrokerApps(),
      refetchContractorApps(),
      refetchVerificationQueue(),
      refetchTickets(),
    ]);
    setRefreshing(false);
  }, [
    refetchStats,
    refetchRevenue,
    refetchUserGrowth,
    refetchBrokerApps,
    refetchContractorApps,
    refetchVerificationQueue,
    refetchTickets,
  ]);

  const formatCurrency = useCallback((amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  }, []);

  const formatNumber = useCallback((num) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  }, []);

  const renderStatCard = (title, value, icon, color, onPress) => (
    <TouchableOpacity
      style={[styles.statCard, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.statHeader}>
        <View style={[styles.statIcon, { backgroundColor: `${color}20` }]}>
          <Ionicons name={icon} size={24} color={color} />
        </View>
        <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>
          {typeof value === 'number' ? formatNumber(value) : value}
        </Text>
      </View>
      <Text style={[styles.statTitle, { color: theme.TEXT_SECONDARY }]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderQuickAction = (title, icon, color, onPress) => (
    <TouchableOpacity
      style={[styles.quickAction, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.actionIcon, { backgroundColor: color }]}>
        <Ionicons name={icon} size={20} color="#fff" />
      </View>
      <Text style={[styles.actionTitle, { color: theme.TEXT_PRIMARY }]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderChart = () => {
    if (!revenueData.chartData) return null;

    return (
      <View style={[styles.chartContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
        <View style={styles.chartHeader}>
          <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>
            Revenue Analytics
          </Text>
          <View style={styles.timeframeSelector}>
            {['7d', '30d', '90d'].map(timeframe => (
              <TouchableOpacity
                key={timeframe}
                style={[
                  styles.timeframeButton,
                  selectedTimeframe === timeframe && { backgroundColor: theme.PRIMARY },
                ]}
                onPress={() => setSelectedTimeframe(timeframe)}
              >
                <Text
                  style={[
                    styles.timeframeText,
                    {
                      color: selectedTimeframe === timeframe ? '#fff' : theme.TEXT_SECONDARY,
                    },
                  ]}
                >
                  {timeframe}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <LineChart
          data={{
            labels: revenueData.chartData?.labels || [],
            datasets: [{
              data: revenueData.chartData?.values || [],
            }],
          }}
          width={width - 64}
          height={200}
          chartConfig={{
            backgroundColor: theme.CARD_BACKGROUND,
            backgroundGradientFrom: theme.CARD_BACKGROUND,
            backgroundGradientTo: theme.CARD_BACKGROUND,
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(${theme.PRIMARY_RGB}, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(${theme.TEXT_SECONDARY_RGB}, ${opacity})`,
            style: {
              borderRadius: 16,
            },
          }}
          bezier
          style={styles.chart}
        />
      </View>
    );
  };

  const renderPendingItems = () => (
    <View style={[styles.pendingContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        Pending Actions
      </Text>
      
      <View style={styles.pendingList}>
        <TouchableOpacity
          style={styles.pendingItem}
          onPress={() => router.push('/Admin/BrokerApplications')}
        >
          <View style={styles.pendingInfo}>
            <Ionicons name="person-add" size={20} color="#FF9800" />
            <Text style={[styles.pendingText, { color: theme.TEXT_PRIMARY }]}>
              Broker Applications
            </Text>
          </View>
          <View style={[styles.pendingBadge, { backgroundColor: '#FF9800' }]}>
            <Text style={styles.pendingCount}>
              {brokerApplications.length}
            </Text>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.pendingItem}
          onPress={() => router.push('/Admin/ContractorApplications')}
        >
          <View style={styles.pendingInfo}>
            <Ionicons name="hammer" size={20} color="#2196F3" />
            <Text style={[styles.pendingText, { color: theme.TEXT_PRIMARY }]}>
              Contractor Applications
            </Text>
          </View>
          <View style={[styles.pendingBadge, { backgroundColor: '#2196F3' }]}>
            <Text style={styles.pendingCount}>
              {contractorApplications.length}
            </Text>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.pendingItem}
          onPress={() => router.push('/Admin/LandVerification')}
        >
          <View style={styles.pendingInfo}>
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            <Text style={[styles.pendingText, { color: theme.TEXT_PRIMARY }]}>
              Land Verifications
            </Text>
          </View>
          <View style={[styles.pendingBadge, { backgroundColor: '#4CAF50' }]}>
            <Text style={styles.pendingCount}>
              {verificationQueue.length}
            </Text>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.pendingItem}
          onPress={() => router.push('/Admin/SupportTickets')}
        >
          <View style={styles.pendingInfo}>
            <Ionicons name="help-circle" size={20} color="#F44336" />
            <Text style={[styles.pendingText, { color: theme.TEXT_PRIMARY }]}>
              Support Tickets
            </Text>
          </View>
          <View style={[styles.pendingBadge, { backgroundColor: '#F44336' }]}>
            <Text style={styles.pendingCount}>
              {supportTickets.total || 0}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <BackButton color="#fff" />
        <Text style={styles.headerTitle}>Admin Dashboard</Text>
        <TouchableOpacity onPress={() => router.push('/Admin/Settings')}>
          <Ionicons name="settings" size={24} color="#fff" />
        </TouchableOpacity>
      </LinearGradient>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.PRIMARY]}
          />
        }
      >
        {/* Stats Overview */}
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            {renderStatCard(
              'Total Users',
              dashboardStats.totalUsers,
              'people',
              '#4CAF50',
              () => router.push('/Admin/UserManagement')
            )}
            {renderStatCard(
              'Active Brokers',
              dashboardStats.activeBrokers,
              'person',
              '#2196F3',
              () => router.push('/Admin/BrokerManagement')
            )}
          </View>
          <View style={styles.statsRow}>
            {renderStatCard(
              'Land Listings',
              dashboardStats.totalLands,
              'location',
              '#FF9800',
              () => router.push('/Admin/LandManagement')
            )}
            {renderStatCard(
              'Total Revenue',
              formatCurrency(dashboardStats.totalRevenue || 0),
              'cash',
              '#9C27B0',
              () => router.push('/Admin/RevenueAnalytics')
            )}
          </View>
        </View>

        {/* Revenue Chart */}
        {renderChart()}

        {/* Quick Actions */}
        <View style={[styles.quickActionsContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Quick Actions
          </Text>
          <View style={styles.quickActionsGrid}>
            {renderQuickAction(
              'User Management',
              'people',
              '#4CAF50',
              () => router.push('/Admin/UserManagement')
            )}
            {renderQuickAction(
              'Content Moderation',
              'shield-checkmark',
              '#FF9800',
              () => router.push('/Admin/ContentModeration')
            )}
            {renderQuickAction(
              'Reports',
              'analytics',
              '#2196F3',
              () => router.push('/Admin/Reports')
            )}
            {renderQuickAction(
              'System Settings',
              'settings',
              '#9C27B0',
              () => router.push('/Admin/Settings')
            )}
          </View>
        </View>

        {/* Pending Actions */}
        {renderPendingItems()}

        <View style={{ height: 20 }} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  statsContainer: {
    padding: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statTitle: {
    fontSize: 12,
    fontWeight: '600',
  },
  chartContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  timeframeSelector: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    padding: 2,
  },
  timeframeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 18,
  },
  timeframeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  quickActionsContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAction: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  pendingContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  pendingList: {
    gap: 12,
  },
  pendingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  pendingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  pendingText: {
    marginLeft: 12,
    fontSize: 14,
    fontWeight: '600',
  },
  pendingBadge: {
    minWidth: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  pendingCount: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default AdminDashboard;
