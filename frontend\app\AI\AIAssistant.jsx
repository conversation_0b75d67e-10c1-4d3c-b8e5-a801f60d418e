import React, { useState, useContext, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import {
  sendChatMessage,
  getChatHistory,
  startNewConversation,
  endConversation,
  getSmartSearchSuggestions,
  getPropertyRecommendations,
  getContractorRecommendations,
} from '../../api/ai/aiApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';

const AIAssistant = () => {
  const { theme } = useContext(ThemeContext);
  const { user } = useContext(AuthContext);
  const router = useRouter();
  const scrollViewRef = useRef(null);
  
  const [message, setMessage] = useState('');
  const [conversationId, setConversationId] = useState(null);
  const [messages, setMessages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState([]);

  // Initialize conversation
  useEffect(() => {
    startNewConversation('general')
      .then(response => {
        setConversationId(response.conversationId);
        setMessages([{
          id: 'welcome',
          type: 'ai',
          content: 'Hello! I\'m your BuildConnect AI assistant. I can help you with property searches, contractor recommendations, market insights, and more. How can I assist you today?',
          timestamp: new Date(),
        }]);
      })
      .catch(error => {
        console.error('Failed to start conversation:', error);
      });
  }, []);

  // Load chat history
  const { refetch: refetchHistory } = useQuery({
    queryKey: ['chatHistory', conversationId],
    queryFn: () => getChatHistory(conversationId),
    enabled: !!conversationId,
    onSuccess: (data) => {
      if (data.messages && data.messages.length > 0) {
        setMessages(data.messages);
      }
    },
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: ({ message, conversationId, context }) => 
      sendChatMessage(message, conversationId, context),
    onMutate: () => {
      setIsTyping(true);
      // Add user message immediately
      const userMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: message,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, userMessage]);
      setMessage('');
    },
    onSuccess: (response) => {
      setIsTyping(false);
      // Add AI response
      const aiMessage = {
        id: response.messageId,
        type: 'ai',
        content: response.message,
        timestamp: new Date(response.timestamp),
        suggestions: response.suggestions,
        actions: response.actions,
      };
      setMessages(prev => [...prev, aiMessage]);
      
      // Update suggestions if provided
      if (response.suggestions) {
        setSuggestions(response.suggestions);
      }
      
      // Scroll to bottom
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    },
    onError: (error) => {
      setIsTyping(false);
      showToast('error', 'Error', 'Failed to send message');
      console.error('Send message error:', error);
    },
  });

  const handleSendMessage = useCallback(() => {
    if (!message.trim() || !conversationId) return;
    
    const context = {
      userId: user?.id,
      userType: user?.role,
      location: user?.location,
    };
    
    sendMessageMutation.mutate({
      message: message.trim(),
      conversationId,
      context,
    });
  }, [message, conversationId, user, sendMessageMutation]);

  const handleSuggestionPress = useCallback((suggestion) => {
    setMessage(suggestion);
  }, []);

  const handleActionPress = useCallback((action) => {
    switch (action.type) {
      case 'search_properties':
        router.push({
          pathname: '/Properties/LandList',
          params: action.params,
        });
        break;
      case 'find_contractors':
        router.push({
          pathname: '/Contractors/ContractorList',
          params: action.params,
        });
        break;
      case 'view_recommendations':
        router.push({
          pathname: '/AI/Recommendations',
          params: action.params,
        });
        break;
      case 'market_analysis':
        router.push({
          pathname: '/AI/MarketAnalysis',
          params: action.params,
        });
        break;
      default:
        break;
    }
  }, [router]);

  const renderMessage = useCallback((msg, index) => {
    const isUser = msg.type === 'user';
    
    return (
      <View
        key={msg.id}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.aiMessageContainer,
        ]}
      >
        {!isUser && (
          <View style={[styles.avatarContainer, { backgroundColor: theme.PRIMARY }]}>
            <Ionicons name="sparkles" size={16} color="#fff" />
          </View>
        )}
        
        <View
          style={[
            styles.messageBubble,
            isUser 
              ? [styles.userMessage, { backgroundColor: theme.PRIMARY }]
              : [styles.aiMessage, { backgroundColor: theme.CARD_BACKGROUND }],
          ]}
        >
          <Text
            style={[
              styles.messageText,
              { color: isUser ? '#fff' : theme.TEXT_PRIMARY },
            ]}
          >
            {msg.content}
          </Text>
          
          {msg.actions && msg.actions.length > 0 && (
            <View style={styles.actionsContainer}>
              {msg.actions.map((action, actionIndex) => (
                <TouchableOpacity
                  key={actionIndex}
                  style={[styles.actionButton, { borderColor: theme.PRIMARY }]}
                  onPress={() => handleActionPress(action)}
                >
                  <Ionicons name={action.icon} size={16} color={theme.PRIMARY} />
                  <Text style={[styles.actionText, { color: theme.PRIMARY }]}>
                    {action.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
        
        {isUser && (
          <View style={[styles.avatarContainer, { backgroundColor: theme.SECONDARY }]}>
            <Ionicons name="person" size={16} color="#fff" />
          </View>
        )}
      </View>
    );
  }, [theme, handleActionPress]);

  const renderSuggestions = () => {
    if (suggestions.length === 0) return null;
    
    return (
      <View style={styles.suggestionsContainer}>
        <Text style={[styles.suggestionsTitle, { color: theme.TEXT_SECONDARY }]}>
          Suggested questions:
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {suggestions.map((suggestion, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.suggestionChip, { backgroundColor: theme.INPUT_BACKGROUND }]}
              onPress={() => handleSuggestionPress(suggestion)}
            >
              <Text style={[styles.suggestionText, { color: theme.TEXT_PRIMARY }]}>
                {suggestion}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (!isTyping) return null;
    
    return (
      <View style={styles.typingContainer}>
        <View style={[styles.avatarContainer, { backgroundColor: theme.PRIMARY }]}>
          <Ionicons name="sparkles" size={16} color="#fff" />
        </View>
        <View style={[styles.typingBubble, { backgroundColor: theme.CARD_BACKGROUND }]}>
          <View style={styles.typingDots}>
            <View style={[styles.typingDot, { backgroundColor: theme.TEXT_SECONDARY }]} />
            <View style={[styles.typingDot, { backgroundColor: theme.TEXT_SECONDARY }]} />
            <View style={[styles.typingDot, { backgroundColor: theme.TEXT_SECONDARY }]} />
          </View>
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <BackButton color="#fff" />
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>AI Assistant</Text>
          <Text style={styles.headerSubtitle}>BuildConnect AI</Text>
        </View>
        <TouchableOpacity onPress={() => router.push('/AI/Settings')}>
          <Ionicons name="settings" size={24} color="#fff" />
        </TouchableOpacity>
      </LinearGradient>

      <ScrollView
        ref={scrollViewRef}
        style={styles.messagesContainer}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
      >
        {messages.map((msg, index) => renderMessage(msg, index))}
        {renderTypingIndicator()}
        <View style={{ height: 20 }} />
      </ScrollView>

      {renderSuggestions()}

      <View style={[styles.inputContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
        <TextInput
          style={[
            styles.textInput,
            {
              backgroundColor: theme.INPUT_BACKGROUND,
              color: theme.TEXT_PRIMARY,
            },
          ]}
          placeholder="Ask me anything about properties, contractors, or market insights..."
          placeholderTextColor={theme.TEXT_PLACEHOLDER}
          value={message}
          onChangeText={setMessage}
          multiline
          maxLength={500}
          onSubmitEditing={handleSendMessage}
          blurOnSubmit={false}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            {
              backgroundColor: message.trim() ? theme.PRIMARY : theme.INPUT_BACKGROUND,
            },
          ]}
          onPress={handleSendMessage}
          disabled={!message.trim() || sendMessageMutation.isLoading}
        >
          {sendMessageMutation.isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Ionicons
              name="send"
              size={20}
              color={message.trim() ? '#fff' : theme.TEXT_PLACEHOLDER}
            />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.8,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messageContainer: {
    flexDirection: 'row',
    marginVertical: 8,
    alignItems: 'flex-end',
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  aiMessageContainer: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  userMessage: {
    borderBottomRightRadius: 4,
  },
  aiMessage: {
    borderBottomLeftRadius: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  actionsContainer: {
    marginTop: 12,
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 20,
  },
  actionText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
  },
  typingContainer: {
    flexDirection: 'row',
    marginVertical: 8,
    alignItems: 'flex-end',
  },
  typingBubble: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderBottomLeftRadius: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  typingDots: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  suggestionsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  suggestionsTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 8,
  },
  suggestionChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  suggestionText: {
    fontSize: 14,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  textInput: {
    flex: 1,
    maxHeight: 100,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    fontSize: 16,
    marginRight: 12,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default AIAssistant;
