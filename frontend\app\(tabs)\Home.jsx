import { useRef, useEffect, useState, useContext } from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    Animated,
    Easing,
    StatusBar,
    SafeAreaView,
} from 'react-native';
import Header from '../Components/Home/Header';
import Sites from '../Components/Home/Sites';
import Brokers from '../Components/Home/Brokers';
import Contractors from '../Components/Home/Contractors';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import QuickAccess from '../Components/Home/QuickAccess';
import { ThemeContext } from '../../context/ThemeContext';

const { height } = Dimensions.get('window');

export default function Home() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const [backgroundHeight, setBackgroundHeight] = useState(height * 0.38);
    const quickAccessRef = useRef(null);
    const scrollY = useRef(new Animated.Value(0)).current;

    // Animations
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(40)).current;
    const backgroundZoom = useRef(new Animated.Value(1)).current;

    // Parallax effect for background image
    const backgroundTranslateY = scrollY.interpolate({
        inputRange: [0, height * 0.38],
        outputRange: [0, -50],
        extrapolate: 'clamp',
    });

    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 700,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 700,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
            Animated.loop(
                Animated.sequence([
                    Animated.timing(backgroundZoom, {
                        toValue: 1.04,
                        duration: 4000,
                        easing: Easing.inOut(Easing.ease),
                        useNativeDriver: true,
                    }),
                    Animated.timing(backgroundZoom, {
                        toValue: 1,
                        duration: 4000,
                        easing: Easing.inOut(Easing.ease),
                        useNativeDriver: true,
                    }),
                ])
            ),
        ]).start();
    }, [fadeAnim, slideAnim, backgroundZoom]);

    // Calculate background height
    const handleQuickAccessLayout = (event) => {
        const { y, height: layoutHeight } = event.nativeEvent.layout;
        setBackgroundHeight(y + layoutHeight + 80);
    };

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />

            {/* Linear Gradient Background from Top */}
            <LinearGradient
                colors={[theme.SECONDARY, theme.PRIMARY, theme.BACKGROUND]}
                style={StyleSheet.absoluteFill}
                start={{ x: 0, y: 0.1 }}
                end={{ x: 0.4, y: 0.5 }}
                pointerEvents="none"
            />

            {/* Banner Background with Parallax */}
            <Animated.View
                style={[
                    styles.bannerContainer,
                    {
                        height: backgroundHeight,
                        transform: [{ translateY: backgroundTranslateY }],
                    },
                ]}
            >
                <Animated.Image
                    source={require('../../assets/images/home_background(2).png')}
                    style={[
                        styles.bannerImage,
                        { transform: [{ scale: backgroundZoom }] },
                    ]}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={[
                        'rgba(30, 136, 229, 0.7)',
                        'rgba(245, 124, 0, 0.7)',
                    ]}
                    style={styles.bannerOverlay}
                    start={{ x: 0, y: 0.3 }}
                    end={{ x: 1, y: 0.8 }}
                />
            </Animated.View>

            <Animated.ScrollView
                style={styles.scrollView}
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
                onScroll={Animated.event(
                    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                    { useNativeDriver: true }
                )}
                scrollEventThrottle={16}
            >
                <Header />

                {/* Banner Card */}
                <Animated.View
                    style={[
                        styles.bannerCard,
                        { backgroundColor: theme.CARD },
                        {
                            opacity: fadeAnim,
                            transform: [{ translateY: slideAnim }],
                        },
                    ]}
                >
                    <Image
                        source={require('../../assets/images/banner(1).png')}
                        style={styles.bannerCardImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(0,0,0,0.5)', 'rgba(0,0,0,0.7)']}
                        style={styles.bannerCardOverlay}
                    >
                        <Text
                            style={[styles.bannerTitle, { color: theme.WHITE }]}
                        >
                            Build Your Future
                        </Text>
                        <Text
                            style={[
                                styles.bannerSubtitle,
                                { color: theme.WHITE },
                            ]}
                        >
                            Connect with trusted contractors and land deals
                        </Text>
                        <TouchableOpacity
                            style={styles.bannerButton}
                            onPress={() => router.push('/Properties/LandList')}
                            activeOpacity={0.85}
                        >
                            <LinearGradient
                                colors={[theme.PRIMARY, theme.SECONDARY]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 1 }}
                                style={styles.bannerButtonGradient}
                            >
                                <Text
                                    style={[
                                        styles.bannerButtonText,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Start Exploring
                                </Text>
                                <Ionicons
                                    name="arrow-forward"
                                    size={18}
                                    color={theme.WHITE}
                                    style={{ marginLeft: 6 }}
                                />
                            </LinearGradient>
                        </TouchableOpacity>
                    </LinearGradient>
                </Animated.View>

                {/* Quick Access Section */}
                <Animated.View
                    ref={quickAccessRef}
                    style={[styles.quickAccessSection, { opacity: fadeAnim }]}
                    onLayout={handleQuickAccessLayout}
                >
                    <QuickAccess />
                </Animated.View>

                {/* Featured Sections */}
                <Sites />
                <View style={styles.divider} />
                <Brokers />
                <View style={styles.divider} />
                <Contractors />

                {/* Support CTA */}
                <Animated.View
                    style={[
                        styles.supportCard,
                        { shadowColor: theme.PRIMARY },
                        { opacity: fadeAnim },
                    ]}
                >
                    <Text
                        style={[styles.supportTitle, { color: theme.PRIMARY }]}
                    >
                        Need Help?
                    </Text>
                    <Text style={styles.supportSubtitle}>
                        Our team is here to assist with any issues or questions.
                    </Text>
                    <TouchableOpacity
                        style={styles.supportButton}
                        onPress={() => router.push('/Support/TicketForm')}
                        activeOpacity={0.85}
                    >
                        <LinearGradient
                            colors={[theme.PRIMARY, theme.SECONDARY]}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                            style={styles.supportButtonGradient}
                        >
                            <Text
                                style={[
                                    styles.supportButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                Raise a Ticket
                            </Text>
                            <Ionicons
                                name="chevron-forward"
                                size={16}
                                color={theme.WHITE}
                                style={styles.supportButtonIcon}
                            />
                        </LinearGradient>
                    </TouchableOpacity>
                </Animated.View>
            </Animated.ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    scrollContent: {
        paddingBottom: 64,
    },
    bannerContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: -1,
        borderBottomLeftRadius: 30,
        borderBottomRightRadius: 30,
        overflow: 'hidden',
    },
    bannerImage: {
        width: '100%',
        height: '100%',
        position: 'absolute',
        opacity: 0.18,
    },
    bannerOverlay: {
        ...StyleSheet.absoluteFillObject,
    },
    bannerCard: {
        height: 200,
        marginHorizontal: 16,
        marginTop: 14,
        borderRadius: 24,
        overflow: 'hidden',
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.18,
        shadowRadius: 20,
        elevation: 10,
    },
    bannerCardImage: {
        width: '100%',
        height: '100%',
        position: 'absolute',
    },
    bannerCardOverlay: {
        flex: 1,
        padding: 22,
        justifyContent: 'flex-end',
    },
    bannerTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        marginBottom: 6,
        textShadowColor: 'rgba(0,0,0,0.4)',
        textShadowOffset: { width: 0, height: 2 },
        textShadowRadius: 6,
    },
    bannerSubtitle: {
        fontSize: 15,
        fontStyle: 'italic',
        marginBottom: 16,
        opacity: 0.95,
    },
    bannerButton: {
        alignSelf: 'flex-end',
    },
    bannerButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 12,
        paddingHorizontal: 24,
        paddingVertical: 12,
    },
    bannerButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    quickAccessSection: {
        marginVertical: 12,
        paddingHorizontal: 16,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#103783',
        marginBottom: 8,
    },
    divider: {
        height: 1,
        backgroundColor: '#e0e7ef',
        marginVertical: 12,
        marginHorizontal: 16,
        borderRadius: 1,
    },
    supportCard: {
        marginHorizontal: 16,
        marginVertical: 28,
        backgroundColor: 'rgba(42,142,158,0.07)',
        padding: 24,
        borderRadius: 24,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.12,
        shadowRadius: 16,
        elevation: 6,
    },
    supportTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    supportSubtitle: {
        color: '#666',
        fontSize: 15,
        marginBottom: 20,
    },
    supportButton: {
        alignSelf: 'flex-start',
    },
    supportButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 14,
        paddingHorizontal: 24,
        paddingVertical: 12,
    },
    supportButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    supportButtonIcon: {
        marginLeft: 10,
    },
    cardContainer: {
        marginHorizontal: 16,
        marginBottom: 24,
    },
});
