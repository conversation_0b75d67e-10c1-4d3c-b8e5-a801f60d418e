import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { Link } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { AuthContext } from '../../context/AuthContext';
import { ThemeContext } from '../../context/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading } = useContext(AuthContext);
  const { theme, isDarkMode } = useContext(ThemeContext);

  const handleLogin = async () => {
    if (!email || !password) {
      return;
    }
    
    await login({ email, password });
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Image 
            source={require('../../assets/images/build-connect.jpg')} 
            style={[styles.logo, { borderColor: theme.LOGO_BORDER }]}
            resizeMode="cover"
          />
          <Text style={[styles.appName, { color: theme.PRIMARY }]}>BuildConnect</Text>
        </View>

        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>Welcome Back</Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>Sign in to continue</Text>

        <View style={[styles.inputContainer, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
          <Ionicons name="mail-outline" size={20} color={theme.TEXT_SECONDARY} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: theme.TEXT_PRIMARY }]}
            placeholder="Email"
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>

        <View style={[styles.inputContainer, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
          <Ionicons name="lock-closed-outline" size={20} color={theme.TEXT_SECONDARY} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: theme.TEXT_PRIMARY }]}
            placeholder="Password"
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
          />
          <TouchableOpacity 
            onPress={() => setShowPassword(!showPassword)}
            style={styles.eyeIcon}
          >
            <Ionicons 
              name={showPassword ? "eye-off-outline" : "eye-outline"} 
              size={20} 
              color={theme.TEXT_SECONDARY} 
            />
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.forgotPassword}>
          <Text style={[styles.forgotPasswordText, { color: theme.PRIMARY }]}>Forgot Password?</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          onPress={handleLogin}
          disabled={isLoading}
        >
          <LinearGradient
            colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.loginButton}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.loginButtonText}>Login</Text>
            )}
          </LinearGradient>
        </TouchableOpacity>

        <View style={styles.registerContainer}>
          <Text style={[styles.registerText, { color: theme.TEXT_SECONDARY }]}>Don't have an account? </Text>
          <Link href="/Register" asChild>
            <TouchableOpacity>
              <Text style={[styles.registerLink, { color: theme.PRIMARY }]}>Register</Text>
            </TouchableOpacity>
          </Link>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
  },
  eyeIcon: {
    padding: 10,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 20,
  },
  forgotPasswordText: {
    fontWeight: '500',
  },
  loginButton: {
    borderRadius: 8,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  registerText: {
    fontSize: 14,
  },
  registerLink: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});