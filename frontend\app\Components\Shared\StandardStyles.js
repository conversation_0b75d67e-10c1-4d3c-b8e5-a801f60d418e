import { StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

/**
 * BuildConnect Standardized Styling Guide
 * 
 * This file contains standardized styling patterns extracted from:
 * - (tabs)/Profile.jsx
 * - Broker/BrokerForm.jsx  
 * - auth/Login.jsx
 * 
 * Use these patterns for consistent UI across the app
 */

// Standard Layout Patterns
export const LAYOUT_PATTERNS = {
  // Main container with background
  MAIN_CONTAINER: {
    flex: 1,
  },
  
  // Background image with gradient overlay
  BACKGROUND_CONTAINER: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    minHeight: height * 0.5,
    zIndex: -1,
  },
  
  BACKGROUND_IMAGE: {
    width: '100%',
    height: '100%',
  },
  
  // Standard gradient overlay
  BACKGROUND_OVERLAY: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  
  // Content container
  CONTENT_CONTAINER: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 60,
  },
  
  // Standard card container
  CARD_CONTAINER: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 20,
    padding: 24,
    elevation: 5,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
};

// Standard Gradient Colors
export const GRADIENT_PATTERNS = {
  // Primary gradient (used in Login, BrokerForm)
  PRIMARY: ['rgba(42, 142, 158, 0.7)', '#2A8E9E'],
  
  // Profile gradient (from ThemeContext)
  PROFILE: (theme) => [theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY],
  
  // Standard gradient props
  GRADIENT_PROPS: {
    start: { x: 0, y: 0 },
    end: { x: 0, y: 1 },
  },
};

// Standard Input Patterns
export const INPUT_PATTERNS = {
  CONTAINER: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  
  ICON: {
    marginRight: 12,
  },
  
  TEXT_INPUT: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
  },
  
  ERROR_BORDER: {
    borderColor: '#FF3B30',
    borderWidth: 2,
  },
};

// Standard Button Patterns
export const BUTTON_PATTERNS = {
  PRIMARY: {
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 8,
  },
  
  SECONDARY: {
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    marginVertical: 8,
  },
  
  TEXT: {
    fontSize: 16,
    fontWeight: '600',
  },
};

// Standard Typography
export const TYPOGRAPHY_PATTERNS = {
  TITLE: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  
  SUBTITLE: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.8,
  },
  
  SECTION_TITLE: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  
  BODY_TEXT: {
    fontSize: 16,
    lineHeight: 24,
  },
  
  CAPTION: {
    fontSize: 14,
    opacity: 0.7,
  },
};

// Standard Spacing
export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48,
};

// Standard Border Radius
export const BORDER_RADIUS = {
  SM: 8,
  MD: 12,
  LG: 16,
  XL: 20,
  ROUND: 50,
};

// Standard Shadows
export const SHADOW_PATTERNS = {
  LIGHT: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  
  MEDIUM: {
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  
  HEAVY: {
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
};

// Helper function to create standardized styles
export const createStandardStyles = (theme) => StyleSheet.create({
  // Main container with background
  container: {
    ...LAYOUT_PATTERNS.MAIN_CONTAINER,
    backgroundColor: theme.BACKGROUND,
  },
  
  // Background setup
  backgroundContainer: LAYOUT_PATTERNS.BACKGROUND_CONTAINER,
  backgroundImage: LAYOUT_PATTERNS.BACKGROUND_IMAGE,
  backgroundOverlay: LAYOUT_PATTERNS.BACKGROUND_OVERLAY,
  
  // Content area
  contentContainer: LAYOUT_PATTERNS.CONTENT_CONTAINER,
  
  // Standard card
  card: {
    ...LAYOUT_PATTERNS.CARD_CONTAINER,
    backgroundColor: theme.CARD,
    shadowColor: theme.SHADOW,
  },
  
  // Typography
  title: {
    ...TYPOGRAPHY_PATTERNS.TITLE,
    color: theme.PRIMARY,
  },
  
  subtitle: {
    ...TYPOGRAPHY_PATTERNS.SUBTITLE,
    color: theme.TEXT_SECONDARY,
  },
  
  sectionTitle: {
    ...TYPOGRAPHY_PATTERNS.SECTION_TITLE,
    color: theme.TEXT_PRIMARY,
  },
  
  bodyText: {
    ...TYPOGRAPHY_PATTERNS.BODY_TEXT,
    color: theme.TEXT_PRIMARY,
  },
  
  caption: {
    ...TYPOGRAPHY_PATTERNS.CAPTION,
    color: theme.TEXT_SECONDARY,
  },
  
  // Input styles
  inputContainer: {
    ...INPUT_PATTERNS.CONTAINER,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: theme.INPUT_BACKGROUND,
  },
  
  inputIcon: INPUT_PATTERNS.ICON,
  
  textInput: {
    ...INPUT_PATTERNS.TEXT_INPUT,
    color: theme.TEXT_PRIMARY,
  },
  
  inputError: INPUT_PATTERNS.ERROR_BORDER,
  
  // Button styles
  primaryButton: {
    ...BUTTON_PATTERNS.PRIMARY,
    backgroundColor: theme.PRIMARY,
  },
  
  secondaryButton: {
    ...BUTTON_PATTERNS.SECONDARY,
    borderColor: theme.PRIMARY,
    backgroundColor: 'transparent',
  },
  
  buttonText: {
    ...BUTTON_PATTERNS.TEXT,
    color: theme.WHITE,
  },
  
  secondaryButtonText: {
    ...BUTTON_PATTERNS.TEXT,
    color: theme.PRIMARY,
  },
});

export default {
  LAYOUT_PATTERNS,
  GRADIENT_PATTERNS,
  INPUT_PATTERNS,
  BUTTON_PATTERNS,
  TYPOGRAPHY_PATTERNS,
  SPACING,
  BORDER_RADIUS,
  SHADOW_PATTERNS,
  createStandardStyles,
};
