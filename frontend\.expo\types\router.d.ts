/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Chats` | `/Chats`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Home` | `/Home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Listings` | `/Listings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/More` | `/More`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Profile` | `/Profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Progress` | `/Progress`; params?: Router.UnknownInputParams; } | { pathname: `/Admin/AdminDashboard`; params?: Router.UnknownInputParams; } | { pathname: `/AI/AIAssistant`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/ApplicationCard`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/ApplicationDetailsModal`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/ApplicationSection`; params?: Router.UnknownInputParams; } | { pathname: `/Applications`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/SkeletonLoader`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/styles`; params?: Router.UnknownInputParams; } | { pathname: `/auth/ForgotPassword`; params?: Router.UnknownInputParams; } | { pathname: `/auth/Login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/SignUp`; params?: Router.UnknownInputParams; } | { pathname: `/auth/VerifyOTP`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/AadhaarStep`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerDetailsStep`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerForm`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerSuccess`; params?: Router.UnknownInputParams; } | { pathname: `/Broker`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/PanStep`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/styles`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/UserDetailsStep`; params?: Router.UnknownInputParams; } | { pathname: `/Chat/ChatList`; params?: Router.UnknownInputParams; } | { pathname: `/Chat/ChatRoom`; params?: Router.UnknownInputParams; } | { pathname: `/Components/HeroSection`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/ChatScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/Users`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Brokers`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Contractors`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/FabModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Header`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/OngoingProjects`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/OptionModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/QuickAccess`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Sites`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/SupportQuickAccess`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/DocumentPreviewModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/Profile`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/ProfileUpdateModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/BackButton`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/Card`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/CategorySection`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/SearchBar`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/ToastConfig`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/AadhaarStep`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/ContractorDetailsStep`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/ContractorForm`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/ContractorSuccess`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/PanStep`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/styles`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/UserDetailsStep`; params?: Router.UnknownInputParams; } | { pathname: `/Map/MapExplorer`; params?: Router.UnknownInputParams; } | { pathname: `/Payment/PaymentScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Applications`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Hiring`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Payments`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Settings`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Tickets`; params?: Router.UnknownInputParams; } | { pathname: `/Properties/LandDetails`; params?: Router.UnknownInputParams; } | { pathname: `/Properties/LandList`; params?: Router.UnknownInputParams; } | { pathname: `/Properties/SellLand`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Chats` | `/Chats`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Home` | `/Home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Listings` | `/Listings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/More` | `/More`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Profile` | `/Profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Progress` | `/Progress`; params?: Router.UnknownOutputParams; } | { pathname: `/Admin/AdminDashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/AI/AIAssistant`; params?: Router.UnknownOutputParams; } | { pathname: `/Applications/ApplicationCard`; params?: Router.UnknownOutputParams; } | { pathname: `/Applications/ApplicationDetailsModal`; params?: Router.UnknownOutputParams; } | { pathname: `/Applications/ApplicationSection`; params?: Router.UnknownOutputParams; } | { pathname: `/Applications`; params?: Router.UnknownOutputParams; } | { pathname: `/Applications/SkeletonLoader`; params?: Router.UnknownOutputParams; } | { pathname: `/Applications/styles`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/ForgotPassword`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/Login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/SignUp`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/VerifyOTP`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/AadhaarStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/BrokerDetailsStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/BrokerForm`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/BrokerSuccess`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/PanStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/styles`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/UserDetailsStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Chat/ChatList`; params?: Router.UnknownOutputParams; } | { pathname: `/Chat/ChatRoom`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/HeroSection`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Chats/ChatScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Chats/Users`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/Brokers`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/Contractors`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/FabModal`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/Header`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/OngoingProjects`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/OptionModal`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/QuickAccess`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/Sites`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/SupportQuickAccess`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Profile/DocumentPreviewModal`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Profile/Profile`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Profile/ProfileUpdateModal`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/BackButton`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/Card`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/CategorySection`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/SearchBar`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/ToastConfig`; params?: Router.UnknownOutputParams; } | { pathname: `/Contractors/AadhaarStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Contractors/ContractorDetailsStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Contractors/ContractorForm`; params?: Router.UnknownOutputParams; } | { pathname: `/Contractors/ContractorSuccess`; params?: Router.UnknownOutputParams; } | { pathname: `/Contractors`; params?: Router.UnknownOutputParams; } | { pathname: `/Contractors/PanStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Contractors/styles`; params?: Router.UnknownOutputParams; } | { pathname: `/Contractors/UserDetailsStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Map/MapExplorer`; params?: Router.UnknownOutputParams; } | { pathname: `/Payment/PaymentScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Applications`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Hiring`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Payments`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Settings`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Tickets`; params?: Router.UnknownOutputParams; } | { pathname: `/Properties/LandDetails`; params?: Router.UnknownOutputParams; } | { pathname: `/Properties/LandList`; params?: Router.UnknownOutputParams; } | { pathname: `/Properties/SellLand`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Chats/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Chats${`?${string}` | `#${string}` | ''}` | `/Chats${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Home${`?${string}` | `#${string}` | ''}` | `/Home${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Listings${`?${string}` | `#${string}` | ''}` | `/Listings${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/More${`?${string}` | `#${string}` | ''}` | `/More${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Profile${`?${string}` | `#${string}` | ''}` | `/Profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Progress${`?${string}` | `#${string}` | ''}` | `/Progress${`?${string}` | `#${string}` | ''}` | `/Admin/AdminDashboard${`?${string}` | `#${string}` | ''}` | `/AI/AIAssistant${`?${string}` | `#${string}` | ''}` | `/Applications/ApplicationCard${`?${string}` | `#${string}` | ''}` | `/Applications/ApplicationDetailsModal${`?${string}` | `#${string}` | ''}` | `/Applications/ApplicationSection${`?${string}` | `#${string}` | ''}` | `/Applications${`?${string}` | `#${string}` | ''}` | `/Applications/SkeletonLoader${`?${string}` | `#${string}` | ''}` | `/Applications/styles${`?${string}` | `#${string}` | ''}` | `/auth/ForgotPassword${`?${string}` | `#${string}` | ''}` | `/auth/Login${`?${string}` | `#${string}` | ''}` | `/auth/SignUp${`?${string}` | `#${string}` | ''}` | `/auth/VerifyOTP${`?${string}` | `#${string}` | ''}` | `/Broker/AadhaarStep${`?${string}` | `#${string}` | ''}` | `/Broker/BrokerDetailsStep${`?${string}` | `#${string}` | ''}` | `/Broker/BrokerForm${`?${string}` | `#${string}` | ''}` | `/Broker/BrokerSuccess${`?${string}` | `#${string}` | ''}` | `/Broker${`?${string}` | `#${string}` | ''}` | `/Broker/PanStep${`?${string}` | `#${string}` | ''}` | `/Broker/styles${`?${string}` | `#${string}` | ''}` | `/Broker/UserDetailsStep${`?${string}` | `#${string}` | ''}` | `/Chat/ChatList${`?${string}` | `#${string}` | ''}` | `/Chat/ChatRoom${`?${string}` | `#${string}` | ''}` | `/Components/HeroSection${`?${string}` | `#${string}` | ''}` | `/Components/Chats/ChatScreen${`?${string}` | `#${string}` | ''}` | `/Components/Chats/Users${`?${string}` | `#${string}` | ''}` | `/Components/Home/Brokers${`?${string}` | `#${string}` | ''}` | `/Components/Home/Contractors${`?${string}` | `#${string}` | ''}` | `/Components/Home/FabModal${`?${string}` | `#${string}` | ''}` | `/Components/Home/Header${`?${string}` | `#${string}` | ''}` | `/Components/Home/OngoingProjects${`?${string}` | `#${string}` | ''}` | `/Components/Home/OptionModal${`?${string}` | `#${string}` | ''}` | `/Components/Home/QuickAccess${`?${string}` | `#${string}` | ''}` | `/Components/Home/Sites${`?${string}` | `#${string}` | ''}` | `/Components/Home/SupportQuickAccess${`?${string}` | `#${string}` | ''}` | `/Components/Profile/DocumentPreviewModal${`?${string}` | `#${string}` | ''}` | `/Components/Profile/Profile${`?${string}` | `#${string}` | ''}` | `/Components/Profile/ProfileUpdateModal${`?${string}` | `#${string}` | ''}` | `/Components/Shared/BackButton${`?${string}` | `#${string}` | ''}` | `/Components/Shared/Card${`?${string}` | `#${string}` | ''}` | `/Components/Shared/CategorySection${`?${string}` | `#${string}` | ''}` | `/Components/Shared/SearchBar${`?${string}` | `#${string}` | ''}` | `/Components/Shared/ToastConfig${`?${string}` | `#${string}` | ''}` | `/Contractors/AadhaarStep${`?${string}` | `#${string}` | ''}` | `/Contractors/ContractorDetailsStep${`?${string}` | `#${string}` | ''}` | `/Contractors/ContractorForm${`?${string}` | `#${string}` | ''}` | `/Contractors/ContractorSuccess${`?${string}` | `#${string}` | ''}` | `/Contractors${`?${string}` | `#${string}` | ''}` | `/Contractors/PanStep${`?${string}` | `#${string}` | ''}` | `/Contractors/styles${`?${string}` | `#${string}` | ''}` | `/Contractors/UserDetailsStep${`?${string}` | `#${string}` | ''}` | `/Map/MapExplorer${`?${string}` | `#${string}` | ''}` | `/Payment/PaymentScreen${`?${string}` | `#${string}` | ''}` | `/Profile/Applications${`?${string}` | `#${string}` | ''}` | `/Profile/Hiring${`?${string}` | `#${string}` | ''}` | `/Profile/Payments${`?${string}` | `#${string}` | ''}` | `/Profile/Settings${`?${string}` | `#${string}` | ''}` | `/Profile/Tickets${`?${string}` | `#${string}` | ''}` | `/Properties/LandDetails${`?${string}` | `#${string}` | ''}` | `/Properties/LandList${`?${string}` | `#${string}` | ''}` | `/Properties/SellLand${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Chats` | `/Chats`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Home` | `/Home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Listings` | `/Listings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/More` | `/More`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Profile` | `/Profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Progress` | `/Progress`; params?: Router.UnknownInputParams; } | { pathname: `/Admin/AdminDashboard`; params?: Router.UnknownInputParams; } | { pathname: `/AI/AIAssistant`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/ApplicationCard`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/ApplicationDetailsModal`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/ApplicationSection`; params?: Router.UnknownInputParams; } | { pathname: `/Applications`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/SkeletonLoader`; params?: Router.UnknownInputParams; } | { pathname: `/Applications/styles`; params?: Router.UnknownInputParams; } | { pathname: `/auth/ForgotPassword`; params?: Router.UnknownInputParams; } | { pathname: `/auth/Login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/SignUp`; params?: Router.UnknownInputParams; } | { pathname: `/auth/VerifyOTP`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/AadhaarStep`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerDetailsStep`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerForm`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerSuccess`; params?: Router.UnknownInputParams; } | { pathname: `/Broker`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/PanStep`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/styles`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/UserDetailsStep`; params?: Router.UnknownInputParams; } | { pathname: `/Chat/ChatList`; params?: Router.UnknownInputParams; } | { pathname: `/Chat/ChatRoom`; params?: Router.UnknownInputParams; } | { pathname: `/Components/HeroSection`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/ChatScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/Users`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Brokers`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Contractors`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/FabModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Header`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/OngoingProjects`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/OptionModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/QuickAccess`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Sites`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/SupportQuickAccess`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/DocumentPreviewModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/Profile`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/ProfileUpdateModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/BackButton`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/Card`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/CategorySection`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/SearchBar`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/ToastConfig`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/AadhaarStep`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/ContractorDetailsStep`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/ContractorForm`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/ContractorSuccess`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/PanStep`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/styles`; params?: Router.UnknownInputParams; } | { pathname: `/Contractors/UserDetailsStep`; params?: Router.UnknownInputParams; } | { pathname: `/Map/MapExplorer`; params?: Router.UnknownInputParams; } | { pathname: `/Payment/PaymentScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Applications`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Hiring`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Payments`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Settings`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Tickets`; params?: Router.UnknownInputParams; } | { pathname: `/Properties/LandDetails`; params?: Router.UnknownInputParams; } | { pathname: `/Properties/LandList`; params?: Router.UnknownInputParams; } | { pathname: `/Properties/SellLand`; params?: Router.UnknownInputParams; } | `/Components/Chats/${Router.SingleRoutePart<T>}${`?${string}` | `#${string}` | ''}` | { pathname: `/Components/Chats/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
