import { privateAPIClient } from '../index';

export const fetchContractorProfile = async () => {
    const url = '/user-service/api/v1/contractors';
    const response = await privateAPIClient.get(url);
    return response.data.application;
};

export const createContractorApplication = async (data) => {
    const response = await privateAPIClient.post(
        '/user-service/api/v1/contractors',
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

export const updateContractorProfile = async (id, data) => {
    const response = await privateAPIClient.patch(
        `/user-service/api/v1/contractors/${id}`,
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

export const deleteContractorApplication = async (id) => {
    const response = await privateAPIClient.delete(
        `/user-service/api/v1/contractors/${id}`
    );
    return response.data;
};
