import { useState, useContext } from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemeContext } from '../../../context/ThemeContext';
import StandardInput from './StandardInput';
import { SPACING } from './StandardStyles';

export default function SearchBar({ onSearch, placeholder = 'Search...' }) {
    const { theme } = useContext(ThemeContext);
    const [searchText, setSearchText] = useState('');

    const handleSearchChange = (text) => {
        setSearchText(text);
        onSearch?.(text);
    };

    return (
        <View style={styles.container}>
            <StandardInput
                placeholder={placeholder}
                value={searchText}
                onChangeText={handleSearchChange}
                iconName="search"
                containerStyle={[
                    styles.searchInput,
                    {
                        backgroundColor: theme.CARD,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
                inputStyle={styles.inputText}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        paddingTop: SPACING.SM,
        paddingHorizontal: SPACING.MD,
    },
    searchInput: {
        borderRadius: 25,
        height: 50,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    inputText: {
        fontSize: 16,
    },
});
