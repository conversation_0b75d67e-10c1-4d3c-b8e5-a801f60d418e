import { View, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function SearchBar() {
    return (
        <View
            style={{
                flexDirection: 'row',
                paddingTop: 10,
                paddingHorizontal: 16,
            }}
        >
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    borderRadius: 40,
                    paddingHorizontal: 16,
                    height: 50,
                    width: '100%',
                    elevation: 2,
                }}
            >
                <Ionicons
                    name="search"
                    size={24}
                    color="gray"
                    style={{ marginRight: 8 }}
                />
                <TextInput
                    placeholder="Search..."
                    style={{ flex: 1, fontSize: 16, color: 'black' }}
                    placeholderTextColor="#999"
                />
            </View>
        </View>
    );
}
