import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';

const DocumentManager = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Mock documents data
  const [documents, setDocuments] = useState([
    {
      id: 1,
      name: 'Aadhaar Card',
      type: 'identity',
      status: 'verified',
      uploadDate: '2024-01-15',
      fileType: 'PDF',
      size: '2.3 MB',
      thumbnail: 'https://via.placeholder.com/100',
    },
    {
      id: 2,
      name: 'PAN Card',
      type: 'identity',
      status: 'verified',
      uploadDate: '2024-01-14',
      fileType: 'PDF',
      size: '1.8 MB',
      thumbnail: 'https://via.placeholder.com/100',
    },
    {
      id: 3,
      name: 'Property Registration',
      type: 'property',
      status: 'pending',
      uploadDate: '2024-01-12',
      fileType: 'PDF',
      size: '5.2 MB',
      thumbnail: 'https://via.placeholder.com/100',
    },
    {
      id: 4,
      name: 'Contractor License',
      type: 'professional',
      status: 'verified',
      uploadDate: '2024-01-10',
      fileType: 'PDF',
      size: '3.1 MB',
      thumbnail: 'https://via.placeholder.com/100',
    },
    {
      id: 5,
      name: 'Bank Statement',
      type: 'financial',
      status: 'rejected',
      uploadDate: '2024-01-08',
      fileType: 'PDF',
      size: '4.5 MB',
      thumbnail: 'https://via.placeholder.com/100',
    },
  ]);

  const categories = [
    { id: 'all', name: 'All Documents', icon: 'folder', count: documents.length },
    { id: 'identity', name: 'Identity', icon: 'person', count: documents.filter(d => d.type === 'identity').length },
    { id: 'property', name: 'Property', icon: 'home', count: documents.filter(d => d.type === 'property').length },
    { id: 'professional', name: 'Professional', icon: 'briefcase', count: documents.filter(d => d.type === 'professional').length },
    { id: 'financial', name: 'Financial', icon: 'card', count: documents.filter(d => d.type === 'financial').length },
  ];

  const filteredDocuments = documents.filter(doc => {
    if (selectedCategory === 'all') return true;
    return doc.type === selectedCategory;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'verified': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'rejected': return '#F44336';
      default: return theme.TEXT_SECONDARY;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified': return 'checkmark-circle';
      case 'pending': return 'time';
      case 'rejected': return 'close-circle';
      default: return 'document';
    }
  };

  const handleUploadDocument = async () => {
    Alert.alert(
      'Upload Document',
      'Choose upload method',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: () => uploadFromCamera() },
        { text: 'Gallery', onPress: () => uploadFromGallery() },
        { text: 'Files', onPress: () => uploadFromFiles() },
      ]
    );
  };

  const uploadFromCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      // Handle camera upload
      console.log('Camera upload:', result);
    }
  };

  const uploadFromGallery = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      // Handle gallery upload
      console.log('Gallery upload:', result);
    }
  };

  const uploadFromFiles = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'image/*'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        // Handle file upload
        console.log('File upload:', result);
      }
    } catch (error) {
      console.error('Document picker error:', error);
    }
  };

  const deleteDocument = (docId) => {
    Alert.alert(
      'Delete Document',
      'Are you sure you want to delete this document?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setDocuments(prev => prev.filter(doc => doc.id !== docId));
          },
        },
      ]
    );
  };

  const renderDocument = (document) => (
    <TouchableOpacity
      key={document.id}
      style={[styles.documentCard, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={() => router.push(`/Documents/DocumentViewer?id=${document.id}`)}
    >
      <View style={styles.documentLeft}>
        <Image source={{ uri: document.thumbnail }} style={styles.documentThumbnail} />
        <View style={styles.documentInfo}>
          <Text style={[styles.documentName, { color: theme.TEXT_PRIMARY }]}>
            {document.name}
          </Text>
          <View style={styles.documentMeta}>
            <Text style={[styles.documentType, { color: theme.TEXT_SECONDARY }]}>
              {document.fileType} • {document.size}
            </Text>
            <Text style={[styles.documentDate, { color: theme.TEXT_SECONDARY }]}>
              {new Date(document.uploadDate).toLocaleDateString()}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.documentRight}>
        <View style={[styles.statusContainer, { backgroundColor: getStatusColor(document.status) + '20' }]}>
          <Ionicons
            name={getStatusIcon(document.status)}
            size={16}
            color={getStatusColor(document.status)}
          />
          <Text style={[styles.statusText, { color: getStatusColor(document.status) }]}>
            {document.status}
          </Text>
        </View>
        
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteDocument(document.id)}
        >
          <Ionicons name="trash-outline" size={20} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderCategory = (category) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryCard,
        {
          backgroundColor: selectedCategory === category.id ? theme.PRIMARY : theme.CARD_BACKGROUND,
        }
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <View style={[
        styles.categoryIcon,
        {
          backgroundColor: selectedCategory === category.id ? 'rgba(255,255,255,0.2)' : theme.PRIMARY + '20',
        }
      ]}>
        <Ionicons
          name={category.icon}
          size={24}
          color={selectedCategory === category.id ? '#fff' : theme.PRIMARY}
        />
      </View>
      <Text style={[
        styles.categoryName,
        {
          color: selectedCategory === category.id ? '#fff' : theme.TEXT_PRIMARY,
        }
      ]}>
        {category.name}
      </Text>
      <Text style={[
        styles.categoryCount,
        {
          color: selectedCategory === category.id ? 'rgba(255,255,255,0.8)' : theme.TEXT_SECONDARY,
        }
      ]}>
        {category.count} files
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Document Manager</Text>
          <TouchableOpacity onPress={handleUploadDocument}>
            <Ionicons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Categories */}
        <View style={styles.categoriesSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Categories
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {categories.map(renderCategory)}
          </ScrollView>
        </View>

        {/* Documents List */}
        <View style={styles.documentsSection}>
          <View style={styles.documentsHeader}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              {selectedCategory === 'all' ? 'All Documents' : categories.find(c => c.id === selectedCategory)?.name}
            </Text>
            <Text style={[styles.documentsCount, { color: theme.TEXT_SECONDARY }]}>
              {filteredDocuments.length} files
            </Text>
          </View>

          {filteredDocuments.length > 0 ? (
            <View style={styles.documentsList}>
              {filteredDocuments.map(renderDocument)}
            </View>
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="document-outline" size={64} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
                No documents found
              </Text>
              <Text style={[styles.emptyStateSubtext, { color: theme.TEXT_SECONDARY }]}>
                Upload your first document to get started
              </Text>
              <TouchableOpacity
                style={[styles.uploadButton, { backgroundColor: theme.PRIMARY }]}
                onPress={handleUploadDocument}
              >
                <Ionicons name="cloud-upload" size={20} color="#fff" />
                <Text style={styles.uploadButtonText}>Upload Document</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  categoriesSection: {
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  categoriesContainer: {
    paddingHorizontal: 20,
  },
  categoryCard: {
    width: 120,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginRight: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  categoryCount: {
    fontSize: 12,
    textAlign: 'center',
  },
  documentsSection: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  documentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  documentsCount: {
    fontSize: 14,
  },
  documentsList: {
    gap: 12,
  },
  documentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  documentLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  documentThumbnail: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 12,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  documentMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  documentType: {
    fontSize: 12,
  },
  documentDate: {
    fontSize: 12,
  },
  documentRight: {
    alignItems: 'flex-end',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  deleteButton: {
    padding: 4,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    marginBottom: 24,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
});

export default DocumentManager;
