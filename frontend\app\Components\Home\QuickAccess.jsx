import { View, Text, TouchableOpacity } from 'react-native';
import React, { useContext } from 'react';
import {
    MaterialIcons,
    Ionicons,
    MaterialCommunityIcons,
} from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { ThemeContext } from '../../../context/ThemeContext';

const values = [
    {
        id: 1,
        name: 'Land',
        icon: 'home-map-marker',
        iconType: 'MaterialCommunityIcons',
        route: '/Properties/LandList',
    },
    {
        id: 2,
        name: 'Contractors',
        icon: 'engineering',
        iconType: 'MaterialIcons',
        route: '/Contractors/ContractorList',
    },
    {
        id: 3,
        name: 'Site Scouts',
        icon: 'person-search',
        iconType: 'MaterialIcons',
        route: '/Brokers/SiteScoutList',
    },
    {
        id: 4,
        name: 'Support',
        icon: 'support-agent',
        iconType: 'MaterialIcons',
        route: '/Support/TicketForm',
    },
];

export default function QuickAccess() {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();

    const renderIcon = (icon, iconType) => {
        if (iconType === 'MaterialIcons') {
            return (
                <MaterialIcons name={icon} size={32} color={theme.PRIMARY} />
            );
        }
        if (iconType === 'MaterialCommunityIcons') {
            return (
                <MaterialCommunityIcons
                    name={icon}
                    size={36}
                    color={theme.PRIMARY}
                />
            );
        }
        return <Ionicons name={icon} size={32} color={theme.WHITE} />;
    };

    return (
        <View className="flex-row px-2 my-2 justify-between">
            {values.map((item) => (
                <TouchableOpacity
                    key={item.id}
                    className="flex-1 mx-1"
                    style={{ maxWidth: '24%' }}
                    onPress={() => router.push(item.route)}
                    activeOpacity={0.8}
                >
                    <View className="items-center">
                        <View
                            className="rounded-lg justify-center items-center mb-2"
                            style={{
                                width: 64,
                                height: 64,
                                backgroundColor: theme.BACKGROUND || '#334155',
                                shadowColor: '#000',
                                // shadowOffset: { width: 0, height: 2 },
                                // shadowOpacity: 0.2,
                                // shadowRadius: 4,
                                //elevation: 4,
                            }}
                        >
                            {renderIcon(item.icon, item.iconType)}
                            <Text
                                className="text-xs text-center text-primary font-semibold"
                                style={{ marginTop: 4 }}
                            >
                                {item.name}
                            </Text>
                        </View>
                    </View>
                </TouchableOpacity>
            ))}
        </View>
    );
}
