import React, { useContext, useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Pressable,
    Animated,
} from 'react-native';
import { ThemeContext } from '../../../context/ThemeContext';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const CustomToast = ({ text1, text2, type, props }) => {
    const router = useRouter();
    const { theme } = useContext(ThemeContext);
    const [show, setShow] = useState(true);

    useEffect(() => {
        setShow(true);
    }, [text1, text2, type, props]);

    if (!show) return null;

    return (
        <Animated.View
            style={[
                styles.container,
                { backgroundColor: theme.TOAST_BLACK, opacity: 0.9 },
            ]}
        >
            {/* Top colored bar */}
            {type === 'success' && (
                <View
                    style={{
                        backgroundColor: theme.SUCCESS,
                        width: 'fit-content',
                        height: 4,
                        borderBottomLeftRadius: 10,
                        borderBottomRightRadius: 10,
                    }}
                />
            )}
            {type === 'error' && (
                <View
                    style={{
                        backgroundColor: theme.ERROR,
                        width: 'fit-content',
                        height: 4,
                        borderBottomLeftRadius: 10,
                        borderBottomRightRadius: 10,
                    }}
                />
            )}
            {type === 'info' && (
                <View
                    style={{
                        backgroundColor: theme.INFO,
                        width: 'fit-content',
                        height: 4,
                        borderBottomLeftRadius: 10,
                        borderBottomRightRadius: 10,
                    }}
                />
            )}

            {/* Close Icon */}
            {props?.close !== false && (
                <TouchableOpacity
                    style={styles.closeIcon}
                    onPress={() => setShow(false)}
                >
                    <Ionicons name="close" size={22} color={theme.WHITE} />
                </TouchableOpacity>
            )}

            <Text
                style={[styles.title, { color: theme.WHITE, paddingRight: 32 }]}
                numberOfLines={2}
            >
                {text1}
            </Text>
            {text2 && (
                <Text
                    style={[styles.message, { color: theme.WHITE }]}
                    numberOfLines={2}
                >
                    {text2}
                </Text>
            )}
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    width: '100%',
                    marginTop: 3,
                }}
            >
                {props?.url && props?.urlText && (
                    <Pressable
                        onPress={() => {
                            router.push(props.url);
                        }}
                        style={{
                            backgroundColor: theme.PRIMARY + '33',
                            paddingHorizontal: 12,
                            paddingVertical: 4,
                            borderRadius: 20,
                        }}
                    >
                        {type === 'success' && (
                            <Text
                                style={{
                                    color: theme.SUCCESS,
                                    fontSize: 12,
                                    fontWeight: '800',
                                }}
                            >
                                {props.urlText}
                            </Text>
                        )}
                        {type === 'error' && (
                            <Text
                                style={{
                                    color: theme.ERROR,
                                    fontSize: 12,
                                    fontWeight: '800',
                                }}
                            >
                                {props.urlText}
                            </Text>
                        )}
                        {type === 'info' && (
                            <Text
                                style={{
                                    color: theme.INFO,
                                    fontSize: 12,
                                    fontWeight: '800',
                                }}
                            >
                                {props.urlText}
                            </Text>
                        )}
                    </Pressable>
                )}
            </View>
        </Animated.View>
    );
};

const toastConfig = {
    success: (props) => <CustomToast {...props} />,
    error: (props) => <CustomToast {...props} />,
    info: (props) => <CustomToast {...props} />,
};

export default toastConfig;

const styles = StyleSheet.create({
    container: {
        minWidth: '60%',
        maxWidth: '80%',
        padding: 10,
        paddingTop: 0,
        borderRadius: 12,
        elevation: 4,
        alignSelf: 'center',
        marginTop: 10,
        marginBottom: 2,
    },
    title: {
        fontWeight: 'bold',
        fontSize: 16,
        marginTop: 8,
        marginBottom: 2,
    },
    message: {
        fontSize: 14,
        marginTop: 2,
        marginBottom: 2,
    },
    closeIcon: {
        position: 'absolute',
        top: 8,
        right: 8,
        zIndex: 2,
        padding: 2,
    },
});
