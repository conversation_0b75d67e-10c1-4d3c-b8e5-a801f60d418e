import React, { useState, useContext, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  RefreshControl,
  TextInput,
  Modal,
  ScrollView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import { fetchLands, searchLands, getLandCategories } from '../../api/land/landApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';

const LandList = () => {
  const { theme } = useContext(ThemeContext);
  const { user } = useContext(AuthContext);
  const router = useRouter();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [sortBy, setSortBy] = useState('newest');
  const [refreshing, setRefreshing] = useState(false);

  // Fetch lands data
  const {
    data: lands = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['lands', selectedCategory, sortBy, priceRange],
    queryFn: () => fetchLands({
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      sortBy,
      minPrice: priceRange.min,
      maxPrice: priceRange.max,
    }),
  });

  // Fetch categories
  const { data: categories = [] } = useQuery({
    queryKey: ['landCategories'],
    queryFn: getLandCategories,
  });

  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim()) return;
    
    try {
      const results = await searchLands({
        query: searchQuery,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        minPrice: priceRange.min,
        maxPrice: priceRange.max,
      });
      // Handle search results
    } catch (error) {
      showToast('error', 'Error', 'Failed to search lands');
    }
  }, [searchQuery, selectedCategory, priceRange]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const handleLandPress = useCallback((land) => {
    router.push({
      pathname: '/Properties/LandDetails',
      params: { landId: land.id },
    });
  }, [router]);

  const handleInterestedPress = useCallback((land) => {
    if (!user) {
      showToast('error', 'Authentication Required', 'Please login to express interest');
      return;
    }
    
    router.push({
      pathname: '/Chat/LandDiscussion',
      params: { 
        landId: land.id, 
        brokerId: land.broker?.id,
        sellerId: land.seller?.id 
      },
    });
  }, [user, router]);

  const renderLandItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.landCard, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={() => handleLandPress(item)}
      activeOpacity={0.8}
    >
      <Image
        source={{ uri: item.images?.[0] || 'https://via.placeholder.com/300x200' }}
        style={styles.landImage}
        resizeMode="cover"
      />
      
      <View style={styles.landInfo}>
        <Text style={[styles.landTitle, { color: theme.TEXT_PRIMARY }]} numberOfLines={2}>
          {item.title}
        </Text>
        
        <View style={styles.locationRow}>
          <Ionicons name="location-outline" size={16} color={theme.PRIMARY} />
          <Text style={[styles.locationText, { color: theme.TEXT_SECONDARY }]} numberOfLines={1}>
            {item.location}
          </Text>
        </View>
        
        <View style={styles.detailsRow}>
          <View style={styles.areaInfo}>
            <Ionicons name="resize-outline" size={16} color={theme.PRIMARY} />
            <Text style={[styles.areaText, { color: theme.TEXT_SECONDARY }]}>
              {item.area} {item.areaUnit}
            </Text>
          </View>
          
          <Text style={[styles.priceText, { color: theme.PRIMARY }]}>
            ₹{item.price?.toLocaleString()}
          </Text>
        </View>
        
        <View style={styles.statusRow}>
          <View style={[
            styles.statusBadge,
            { backgroundColor: item.verificationStatus === 'verified' ? '#4CAF50' : '#FF9800' }
          ]}>
            <Text style={styles.statusText}>
              {item.verificationStatus === 'verified' ? 'Verified' : 'Pending'}
            </Text>
          </View>
          
          <TouchableOpacity
            style={[styles.interestedButton, { backgroundColor: theme.PRIMARY }]}
            onPress={() => handleInterestedPress(item)}
          >
            <Text style={styles.interestedText}>Interested</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderFilterModal = () => (
    <Modal
      visible={showFilters}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowFilters(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.filterModal, { backgroundColor: theme.CARD_BACKGROUND }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
              Filter Lands
            </Text>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Ionicons name="close" size={24} color={theme.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.filterContent}>
            {/* Category Filter */}
            <Text style={[styles.filterLabel, { color: theme.TEXT_PRIMARY }]}>
              Category
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <TouchableOpacity
                style={[
                  styles.categoryChip,
                  selectedCategory === 'all' && { backgroundColor: theme.PRIMARY }
                ]}
                onPress={() => setSelectedCategory('all')}
              >
                <Text style={[
                  styles.categoryText,
                  { color: selectedCategory === 'all' ? '#fff' : theme.TEXT_SECONDARY }
                ]}>
                  All
                </Text>
              </TouchableOpacity>
              
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryChip,
                    selectedCategory === category.id && { backgroundColor: theme.PRIMARY }
                  ]}
                  onPress={() => setSelectedCategory(category.id)}
                >
                  <Text style={[
                    styles.categoryText,
                    { color: selectedCategory === category.id ? '#fff' : theme.TEXT_SECONDARY }
                  ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            
            {/* Price Range */}
            <Text style={[styles.filterLabel, { color: theme.TEXT_PRIMARY }]}>
              Price Range
            </Text>
            <View style={styles.priceRangeRow}>
              <TextInput
                style={[styles.priceInput, { backgroundColor: theme.INPUT_BACKGROUND }]}
                placeholder="Min Price"
                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                value={priceRange.min}
                onChangeText={(text) => setPriceRange(prev => ({ ...prev, min: text }))}
                keyboardType="numeric"
              />
              <Text style={[styles.toText, { color: theme.TEXT_SECONDARY }]}>to</Text>
              <TextInput
                style={[styles.priceInput, { backgroundColor: theme.INPUT_BACKGROUND }]}
                placeholder="Max Price"
                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                value={priceRange.max}
                onChangeText={(text) => setPriceRange(prev => ({ ...prev, max: text }))}
                keyboardType="numeric"
              />
            </View>
            
            {/* Sort By */}
            <Text style={[styles.filterLabel, { color: theme.TEXT_PRIMARY }]}>
              Sort By
            </Text>
            {['newest', 'oldest', 'price_low', 'price_high', 'area_small', 'area_large'].map((option) => (
              <TouchableOpacity
                key={option}
                style={styles.sortOption}
                onPress={() => setSortBy(option)}
              >
                <Ionicons
                  name={sortBy === option ? 'radio-button-on' : 'radio-button-off'}
                  size={20}
                  color={theme.PRIMARY}
                />
                <Text style={[styles.sortText, { color: theme.TEXT_PRIMARY }]}>
                  {option.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <TouchableOpacity
            style={[styles.applyButton, { backgroundColor: theme.PRIMARY }]}
            onPress={() => setShowFilters(false)}
          >
            <Text style={styles.applyButtonText}>Apply Filters</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <BackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.TEXT_PRIMARY }]}>
            Failed to load lands
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
            onPress={refetch}
          >
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <BackButton color="#fff" />
        <Text style={styles.headerTitle}>Available Lands</Text>
        <TouchableOpacity onPress={() => setShowFilters(true)}>
          <Ionicons name="filter" size={24} color="#fff" />
        </TouchableOpacity>
      </LinearGradient>
      
      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
        <Ionicons name="search" size={20} color={theme.TEXT_PLACEHOLDER} />
        <TextInput
          style={[styles.searchInput, { color: theme.TEXT_PRIMARY }]}
          placeholder="Search lands..."
          placeholderTextColor={theme.TEXT_PLACEHOLDER}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={handleSearch}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={theme.TEXT_PLACEHOLDER} />
          </TouchableOpacity>
        )}
      </View>
      
      <FlatList
        data={lands}
        renderItem={renderLandItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.PRIMARY]}
          />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="home-outline" size={64} color={theme.TEXT_PLACEHOLDER} />
            <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
              No lands found
            </Text>
          </View>
        }
      />
      
      {renderFilterModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  listContainer: {
    padding: 16,
  },
  landCard: {
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  landImage: {
    width: '100%',
    height: 200,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  landInfo: {
    padding: 16,
  },
  landTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    marginLeft: 4,
    fontSize: 14,
    flex: 1,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  areaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  areaText: {
    marginLeft: 4,
    fontSize: 14,
  },
  priceText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  interestedButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  interestedText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  filterModal: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterContent: {
    padding: 20,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    marginTop: 16,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f0f0f0',
  },
  categoryText: {
    fontSize: 14,
  },
  priceRangeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  priceInput: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  toText: {
    marginHorizontal: 8,
  },
  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  sortText: {
    marginLeft: 12,
    fontSize: 16,
  },
  applyButton: {
    margin: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  applyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
  },
});

export default LandList;
