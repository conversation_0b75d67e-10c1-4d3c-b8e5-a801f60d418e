import { Linking } from 'react-native';
import CategorySection from '../Shared/CategorySection';

const contractors = [
    {
        id: '1',
        title: 'ABC Builders',
        description:
            'Specializing in commercial construction with 15+ years experience',
        image: require('../../../assets/images/image1.png'),
        url: 'https://example.com/contractor1',
    },
    {
        id: '2',
        title: 'Elite Construction',
        description: 'Premium residential and commercial construction services',
        image: require('../../../assets/images/image2.png'),
        url: 'https://example.com/contractor2',
    },
    {
        id: '3',
        title: 'Modern Homes',
        description: 'Custom home building and renovation experts',
        image: require('../../../assets/images/image3.png'),
        url: 'https://example.com/contractor3',
    },
    {
        id: '4',
        title: 'Pro Builders',
        description: 'Reliable construction services for all project sizes',
        image: require('../../../assets/images/icon.png'),
        url: 'https://example.com/contractor4',
    },
];

export default function Contractors() {
    const openContractor = (url) => {
        Linking.openURL(url);
    };

    return (
        <CategorySection
            title="Top Contractors"
            data={contractors}
            onItemPress={openContractor}
            viewAllRoute="/Contractors/ContractorList"
        />
    );
}
