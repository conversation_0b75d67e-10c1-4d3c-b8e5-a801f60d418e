import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  useCallback,
} from "react";
import {
  View,
  Image,
  ActivityIndicator,
  ScrollView,
  Text,
  Platform,
  KeyboardAvoidingView,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useQuery, useMutation } from "@tanstack/react-query";
import queryClient from "../../api/queryClient";
import { useFormik } from "formik";
import { ThemeContext } from "../../context/ThemeContext";
import { fetchUserProfile } from "../../api/user/userApi";
import { showToast } from "../../utils/showToast";
import BackButton from "../Components/Shared/BackButton";
import DocumentPreviewModal from "../Components/Profile/DocumentPreviewModal";
import UserDetailsStep from "./UserDetailsStep";
import AadhaarStep from "./AadhaarStep";
import PanStep from "./PanStep";
import BrokerDetailsStep from "./BrokerDetailsStep";
import {styles} from "./styles";
import { getFileNameFromUrl } from "../../utils/BrokerFormUtils";
import {
  updateBrokerProfile,
  createBrokerApplication,
} from "../../api/broker/brokerApi";
import { validationSchema } from "../../utils/validateSchema";

const BrokerForm = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const { editData } = useLocalSearchParams();
  const [step, setStep] = useState("userDetails");
  const [showDocumentPreviewModal, setShowDocumentPreviewModal] =
    useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const scrollViewRef = useRef(null);
const [isSubmitting, setIsSubmitting] = useState(false);

  let parsedEditData = null;
  try {
    parsedEditData = editData ? JSON.parse(editData) : null;
  } catch (error) {
    showToast(
      "error",
      "Invalid Data",
      "Failed to load existing application data."
    );
  }
  const isEditing = !!parsedEditData;
  const brokerId = parsedEditData?.id;

  const originalDocuments = useRef({
    aadhaarDocument: parsedEditData?.aadhaarDocument || null,
    panDocument: parsedEditData?.panDocument || null,
  });

  useEffect(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ y: 0, animated: true });
    }
  }, [step]);

  const {
    data: user,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["userProfile"],
    queryFn: fetchUserProfile,
    refetchInterval: isEditing ? 30000 : false, // Poll every 30 seconds only in update mode
    refetchIntervalInBackground: false, // Disable polling in background
    enabled: true, // Always enabled, but refetchInterval only applies when isEditing
    onError: () => {
      showToast("error", "Error", "Failed to fetch user profile.");
    },
  });

  const { mutate: updateBroker, isLoading: isUpdating } = useMutation({
    mutationFn: ({ brokerId, data }) => updateBrokerProfile(brokerId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["brokerApplication", brokerId],
      });
      showToast(
        "success",
        "Success",
        "Broker application updated successfully."
      );
      router.push("/Profile/Applications");
    },
    onError: (error) => {
      if (error.response?.status === 409) {
        showToast(
          "error",
          "Error",
          "You are already registered as a Site Scout."
        );
      } else if (error.response?.status === 400) {
        showToast("error", "Invalid Data", error.response.data.error);
      } else {
        showToast("error", "Submission Error", "Failed to submit application.");
      }
    },
  });

  const { mutate: createBroker, isLoading: isCreating } = useMutation({
    mutationFn: createBrokerApplication,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["brokerApplication"] });
      router.push("/Broker/BrokerSuccess");
    },
    onError: (error) => {
      if (error.response?.status === 409) {
        showToast(
          "error",
          "Error",
          "You are already registered as a Site Scout."
        );
      } else if (error.response?.status === 400) {
        showToast("error", "Invalid Data", error.response.data.error);
      } else {
        showToast("error", "Submission Error", "Failed to submit application.");
      }
    },
  });


  const formik = useFormik({
    initialValues: {
      aadhaarNumber: parsedEditData?.aadhaarNumber || "",
      nameOnAadhaar: parsedEditData?.nameOnAadhaar || "",
      dateOfBirth: parsedEditData?.dateOfBirth
        ? new Date(parsedEditData.dateOfBirth)
        : "",
      gender: parsedEditData?.gender || "",
      address: parsedEditData?.address || "",
      panNumber: parsedEditData?.panNumber || "",
      panName: parsedEditData?.panName || "",
      panDateOfBirth: parsedEditData?.panDateOfBirth
        ? new Date(parsedEditData.panDateOfBirth)
        : "",
      experience: parsedEditData?.experience || null,
      serviceAreas: Array.isArray(parsedEditData?.serviceAreas)
        ? parsedEditData.serviceAreas
        : [],
      aadhaarDocument: parsedEditData?.aadhaarDocument || null,
      panDocument: parsedEditData?.panDocument || null,
      brokerId: brokerId || null,
    },
    validationSchema,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: async (values) => {
      setIsSubmitting(true); // <-- Move this to the top, before any async/mutation
      try {
        const formPayload = new FormData();

        // Always append service areas (limit to 10)
        values.serviceAreas.slice(0, 10).forEach((area) => {
          formPayload.append("serviceAreas", area);
        });

  if (
    values.panDocument &&
    typeof values.panDocument !== "string" &&
    values.panDocument.uri
  ) {
    formPayload.append("panDocument", {
      uri: values.panDocument.uri,
      name: values.panDocument.name || "panDocument.jpg",
      type: values.panDocument.type || "image/jpeg",
    });
  }

  if (
    values.aadhaarDocument &&
    typeof values.aadhaarDocument !== "string" &&
    values.aadhaarDocument.uri
  ) {
    formPayload.append("aadhaarDocument", {
      uri: values.aadhaarDocument.uri,
      name: values.aadhaarDocument.name || "aadhaarDocument.jpg",
      type: values.aadhaarDocument.type || "image/jpeg",
    });
  }


        formPayload.append("experience", values.experience);
        formPayload.append("aadhaarNumber", values.aadhaarNumber);
        formPayload.append("nameOnAadhaar", values.nameOnAadhaar);
        formPayload.append("gender", values.gender);
        formPayload.append("address", values.address);
        formPayload.append("panNumber", values.panNumber);
        formPayload.append("panName", values.panName);

        // Append valid date strings only
        if (values.dateOfBirth instanceof Date && !isNaN(values.dateOfBirth)) {
          formPayload.append("dateOfBirth", values.dateOfBirth.toISOString());
        }

        if (values.panDateOfBirth instanceof Date && !isNaN(values.panDateOfBirth)) {
          formPayload.append("panDateOfBirth", values.panDateOfBirth.toISOString());
        }

        // Trigger appropriate mutation
        if (isEditing) {
          await new Promise((resolve, reject) => {
            updateBroker(
              { brokerId, data: formPayload },
              {
                onSuccess: resolve,
                onError: reject,
              }
            );
          });
        } else {
          await new Promise((resolve, reject) => {
            createBroker(formPayload, {
              onSuccess: resolve,
              onError: reject,
            });
          });
        }
      } finally {
        setIsSubmitting(false);
        formik.setSubmitting(false);
      }
    },
  });

  const handlePreviewDocument = useCallback((document) => {
    if (document) {
      const uri = typeof document === "string" ? document : document.uri;
      setSelectedDocument(uri);
      setShowDocumentPreviewModal(true);
    }
  }, []);

  if (isLoading) {
    return <ActivityIndicator size="large" color={theme.PRIMARY} />;
  }

  if (isError) {
    return (
      <Text style={{ color: theme.TEXT_PRIMARY }}>Error: {error.message}</Text>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
    >
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <BackButton color={theme.WHITE} />
        <View style={styles.backgroundContainer}>
          <Image
            source={require("../../assets/images/background.png")}
            style={styles.backgroundImage}
            resizeMode="cover"
          />
          <LinearGradient
            colors={["rgba(42, 142, 158, 0.7)", theme.PRIMARY]}
            style={styles.backgroundOverlay}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />
        </View>
        <View style={styles.contentContainer}>
          <View
            style={[
              styles.formContainer,
              { shadowColor: theme.SHADOW, backgroundColor: theme.CARD },
            ]}
          >
            <Text style={[styles.title, { color: theme.PRIMARY }]}>
              {isEditing
                ? "Edit Site Scout Application"
                : "Site Scout Application"}
            </Text>
            <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
              {isEditing
                ? "Update your broker details"
                : "Apply to become a trusted broker"}
            </Text>
            <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
              Step{" "}
              {["userDetails", "aadhaar", "pan", "brokerDetails"].indexOf(
                step
              ) + 1}{" "}
              of 4
            </Text>
            {step === "userDetails" && (
              <UserDetailsStep theme={theme} user={user} setStep={setStep} />
            )}
            {step === "aadhaar" && (
              <AadhaarStep
                formik={formik}
                theme={theme}
                setStep={setStep}
                handlePreviewDocument={handlePreviewDocument}
                isUploading={isCreating || isUpdating}
              />
            )}
            {step === "pan" && (
              <PanStep
                formik={formik}
                theme={theme}
                setStep={setStep}
                handlePreviewDocument={handlePreviewDocument}
                isUploading={isCreating || isUpdating}
              />
            )}
            {step === "brokerDetails" && (
              <BrokerDetailsStep
                formik={formik}
                theme={theme}
                setStep={setStep}
                isSubmitting={isSubmitting}
              />
            )}
          </View>
        </View>
      </ScrollView>
      <DocumentPreviewModal
        visible={showDocumentPreviewModal}
        documentUrl={selectedDocument}
        documentType=""
        onClose={() => {
          setShowDocumentPreviewModal(false);
          setSelectedDocument(null);
        }}
      />
    </KeyboardAvoidingView>
  );
};

export default BrokerForm;
