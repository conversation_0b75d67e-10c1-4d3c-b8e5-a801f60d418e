import { View, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useState, useContext } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons';
import OptionModal from './OptionModal';
import { ThemeContext } from '../../../context/ThemeContext';
import { SPACING, BORDER_RADIUS } from '../Shared/StandardStyles';

export default function Header() {
    const { theme } = useContext(ThemeContext);
    const [OptionModalVisible, setOptionModalVisible] = useState(false);

    return (
        <View style={styles.container}>
            <OptionModal
                OptionModalVisible={OptionModalVisible}
                setOptionModalVisible={setOptionModalVisible}
            />

            <View style={styles.logoContainer}>
                <Image
                    source={require('../../../assets/images/logo.png')}
                    style={[
                        styles.logo,
                        {
                            borderColor: theme.WHITE,
                        },
                    ]}
                />
            </View>

            <View style={styles.actionsContainer}>
                <TouchableOpacity style={styles.iconButton}>
                    <Ionicons
                        name="search"
                        size={24}
                        color={theme.WHITE}
                    />
                </TouchableOpacity>

                <TouchableOpacity style={styles.iconButton}>
                    <Ionicons
                        name="notifications"
                        size={24}
                        color={theme.WHITE}
                    />
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.iconButton}
                    onPress={() => setOptionModalVisible(true)}
                >
                    <SimpleLineIcons
                        name="options-vertical"
                        size={20}
                        color={theme.WHITE}
                    />
                </TouchableOpacity>
            </View>
        </View>
    );
}

const styles = {
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: SPACING.MD,
        paddingVertical: SPACING.SM,
    },
    logoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    logo: {
        height: 40,
        width: 40,
        borderRadius: BORDER_RADIUS.ROUND,
        borderWidth: 0.5,
    },
    actionsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    iconButton: {
        padding: SPACING.SM,
        borderRadius: BORDER_RADIUS.ROUND,
    },
};
