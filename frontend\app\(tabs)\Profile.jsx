// Profile.jsx
import React, { useEffect, useContext, useMemo, useState } from 'react';
import {
    ScrollView,
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    ActivityIndicator,
    StatusBar,
    Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';
import ProfileUpdateModal from '../Components/Profile/ProfileUpdateModal';
import { useQuery, useMutation } from '@tanstack/react-query';
import { fetchUserProfile, updateUserProfile } from '../../api/user/userApi';
import queryClient from '../../api/queryClient';
import BackButton from '../Components/Shared/BackButton';

const { height } = Dimensions.get('window');

const PROFILE_OPTIONS = [
    { key: 'professional', icon: 'briefcase-outline', label: 'Professional Details', visibleto: ['contractor', 'broker'], url: '/Profile/Professional' },
    { key: 'applications', icon: 'receipt-outline', label: 'Manage Applications', visibleto: ['user', 'contractor', 'broker'], url: '/Profile/Applications' },
    { key: 'payments', icon: 'card-outline', label: 'Manage Payments', visibleto: ['user', 'contractor', 'broker'], url: '/Profile/Payments' },
    { key: 'hiring', icon: 'hourglass-outline', label: 'Hiring History', visibleto: ['user'], url: '/Profile/Hiring' },
    { key: 'tickets', icon: 'chatbubbles-outline', label: 'Raise Tickets', visibleto: ['user', 'contractor', 'broker'], url: '/Profile/Tickets' },
    { key: 'settings', icon: 'settings-outline', label: 'Settings', visibleto: ['user', 'contractor', 'broker'], url: '/Profile/Settings' },
    { key: 'logout', icon: 'log-out-outline', label: 'Logout', visibleto: ['user', 'contractor', 'broker'], url: '/auth/Login' },
];

export default function ProfileSection() {
    const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
    const router = useRouter();
    const [user, setUser] = useState({ name: '', email: '', phone: '', avatar: null, role: '' });
    const [isExtendCard, setExtendCard] = useState(false);

    const { data: userData, isLoading: isUserLoading, error: userError } = useQuery({
        queryKey: ['userProfile'],
        queryFn: fetchUserProfile,
        onError: () => showToast('error', 'Error', 'Failed to fetch user profile.')
    });

    const { mutate: updateProfile, isLoading: isUpdating } = useMutation({
        mutationFn: updateUserProfile,
        onSuccess: (response) => {
            // Update local state with the response data (including new Cloudinary URL if image was uploaded)
            if (response && response.user) {
                setUser(response.user);
            }
            queryClient.invalidateQueries({ queryKey: ['userProfile'] });
            showToast('success', 'Success', 'Profile updated successfully.');
        },
        onError: (error) => {
            console.error('Profile update error:', error);
            showToast('error', 'Error', 'Failed to update profile.');
        }
    });

    useEffect(() => {
        if (userData) setUser(userData);
    }, [userData]);

    const handleSaveProfile = (updatedData) => {
        updateProfile(updatedData);
    };

    const handleLogout = () => {
        Alert.alert('Confirm Logout', 'Are you sure you want to log out?', [
            { text: 'Cancel', style: 'cancel' },
            {
                text: 'Logout', style: 'destructive', onPress: async () => {
                    try {
                        showToast('success', 'Logout', 'Logout Successful');
                        await AsyncStorage.removeItem('accessToken');
                        await AsyncStorage.removeItem('sessionId');
                        router.replace('/auth/Login');
                    } catch {
                        showToast('error', 'Error', 'Failed to logout.');
                    }
                }
            },
        ]);
    };

    const handleOptionPress = (url, key) => key === 'logout' ? handleLogout() : router.push(url);

    const filteredOptions = useMemo(
        () => PROFILE_OPTIONS.filter((option) => option.visibleto.includes(user.role?.toLowerCase())),
        [user.role]
    );

    if (isUserLoading) return <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.BACKGROUND }}><ActivityIndicator size="large" color={theme.PRIMARY} /></View>;
    if (userError) return <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.BACKGROUND }}><Text style={{ color: theme.TEXT_PRIMARY }}>Failed to load profile. Please try again.</Text></View>;

    return (
        <View style={{ flex: 1, backgroundColor: theme.BACKGROUND }}>
            <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} backgroundColor={theme.PRIMARY} />
            <ScrollView contentContainerStyle={{ flexGrow: 1, minHeight: height }}>
                <BackButton color={theme.WHITE} testID="back-button" />

                <View style={{ position: 'absolute', top: 0, left: 0, right: 0, minHeight: height * 0.5, zIndex: -1 }}>
                    <Image source={require('../../assets/images/background.png')} style={{ width: '100%', height: '100%' }} resizeMode="cover" />
                    <LinearGradient
                        colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
                        style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}
                        start={{ x: 0, y: 0 }} end={{ x: 0, y: 1 }}
                    />
                </View>

                <View style={{ flex: 1, alignItems: 'center' }}>
                    <View style={{ width: '90%', maxWidth: 400, backgroundColor: theme.CARD, borderRadius: 20, padding: 24, elevation: 5 }}>
                        <View style={{ alignItems: 'center', position: 'relative' }}>
                            <TouchableOpacity
                                style={{ position: 'absolute', top: -16, right: -16, padding: 8, backgroundColor: theme.PRIMARY + '25', borderRadius: 20 }}
                                onPress={toggleTheme}
                                accessibilityLabel="Toggle Dark Mode"
                                testID="theme-toggle"
                            >
                                <Ionicons name={isDarkMode ? 'sunny-outline' : 'moon-outline'} size={24} color={theme.PRIMARY} />
                            </TouchableOpacity>

                            <View style={styles.profileContainer}>
                                <View style={[styles.avatarContainer, { backgroundColor: theme.PRIMARY }]}>
                                    {user.avatar && typeof user.avatar === 'string' ? (
                                        <Image
                                            source={{ uri: user.avatar }}
                                            style={styles.avatarImage}
                                            accessibilityLabel={`Profile picture for ${user.name}`}
                                            onError={(error) => {
                                                console.log('Failed to load profile image:', error);
                                                setUser(prev => ({ ...prev, avatar: null }));
                                            }}
                                        />
                                    ) : (
                                        <Text style={[styles.avatarText, { color: theme.WHITE }]}>
                                            {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                                        </Text>
                                    )}
                                </View>

                                <View style={styles.userInfoContainer}>
                                    <View style={styles.nameContainer}>
                                        <Text
                                            style={[styles.userName, { color: theme.TEXT_PRIMARY }]}
                                            numberOfLines={2}
                                            ellipsizeMode="tail"
                                        >
                                            {user.name || 'User'}
                                        </Text>
                                        <TouchableOpacity
                                            onPress={() => setExtendCard(true)}
                                            accessibilityLabel="Edit Profile"
                                            testID="edit-profile"
                                            style={styles.editButton}
                                        >
                                            <Ionicons name="create-outline" size={20} color={theme.PRIMARY} />
                                        </TouchableOpacity>
                                    </View>

                                    <Text
                                        style={[styles.userEmail, { color: theme.TEXT_SECONDARY }]}
                                        numberOfLines={2}
                                        ellipsizeMode="middle"
                                    >
                                        {user.email || 'No email provided'}
                                    </Text>

                                    <Text
                                        style={[styles.userPhone, { color: theme.TEXT_SECONDARY }]}
                                        numberOfLines={1}
                                        ellipsizeMode="tail"
                                    >
                                        {user.phone || 'No phone provided'}
                                    </Text>

                                    {user.role && (
                                        <View style={[styles.roleBadge, { backgroundColor: theme.PRIMARY + '20' }]}>
                                            <Text style={[styles.roleText, { color: theme.PRIMARY }]}>
                                                {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            </View>
                        </View>

                        <View style={{ borderTopWidth: StyleSheet.hairlineWidth, borderTopColor: theme.INPUT_BORDER, marginTop: 16 }}>
                            {filteredOptions.map(({ key, icon, label, url }) => (
                                <TouchableOpacity
                                    key={key}
                                    style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: 15, borderBottomWidth: StyleSheet.hairlineWidth, borderBottomColor: theme.INPUT_BORDER }}
                                    onPress={() => handleOptionPress(url, key)}
                                    accessibilityLabel={label}
                                    accessibilityRole="button"
                                    testID={`${key}-option`}
                                >
                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                        <Ionicons name={icon} size={22} color={theme.PRIMARY} />
                                        <Text style={{ marginLeft: 15, fontSize: 16, color: theme.TEXT_PRIMARY }}>{label}</Text>
                                    </View>
                                    <Ionicons name="chevron-forward-outline" size={20} color={theme.TEXT_SECONDARY} />
                                </TouchableOpacity>
                            ))}
                        </View>
                    </View>
                </View>

                <ProfileUpdateModal
                    isVisible={isExtendCard}
                    onClose={() => setExtendCard(false)}
                    theme={theme}
                    userData={user}
                    onSave={handleSaveProfile}
                    isLoading={isUpdating}
                />
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    profileContainer: {
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
        marginTop: 30,
        paddingHorizontal: 20,
    },
    avatarContainer: {
        borderRadius: 50,
        width: 100,
        height: 100,
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        marginBottom: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 5,
    },
    avatarImage: {
        width: '100%',
        height: '100%',
    },
    avatarText: {
        fontSize: 36,
        fontWeight: 'bold',
    },
    userInfoContainer: {
        width: '100%',
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    nameContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 12,
        width: '100%',
    },
    userName: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        flex: 1,
        marginRight: 8,
    },
    editButton: {
        padding: 8,
        borderRadius: 20,
        backgroundColor: 'rgba(0,0,0,0.05)',
    },
    userEmail: {
        fontSize: 16,
        marginBottom: 8,
        textAlign: 'center',
        width: '100%',
        paddingHorizontal: 10,
    },
    userPhone: {
        fontSize: 16,
        marginBottom: 12,
        textAlign: 'center',
        width: '100%',
    },
    roleBadge: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        marginTop: 8,
    },
    roleText: {
        fontSize: 14,
        fontWeight: '600',
        textAlign: 'center',
    },
});
