import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

const MarketAnalytics = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState('6m');
  const [selectedLocation, setSelectedLocation] = useState('bangalore');

  const periods = [
    { id: '1m', name: '1M' },
    { id: '3m', name: '3M' },
    { id: '6m', name: '6M' },
    { id: '1y', name: '1Y' },
    { id: 'all', name: 'All' },
  ];

  const locations = [
    { id: 'bangalore', name: 'Bangalore' },
    { id: 'mumbai', name: 'Mumbai' },
    { id: 'delhi', name: 'Delhi' },
    { id: 'pune', name: 'Pune' },
    { id: 'hyderabad', name: 'Hyderabad' },
  ];

  // Mock analytics data
  const marketData = {
    priceIndex: {
      current: 4850,
      change: 8.5,
      trend: 'up',
    },
    averagePrice: {
      residential: '₹4,200/sq ft',
      commercial: '₹8,500/sq ft',
      plots: '₹2,800/sq ft',
    },
    marketActivity: {
      totalListings: 12450,
      newListings: 340,
      soldProperties: 156,
      averageDays: 45,
    },
    topAreas: [
      { name: 'Whitefield', avgPrice: '₹5,200/sq ft', growth: '+12%' },
      { name: 'Electronic City', avgPrice: '₹4,800/sq ft', growth: '+8%' },
      { name: 'Koramangala', avgPrice: '₹6,500/sq ft', growth: '+15%' },
      { name: 'HSR Layout', avgPrice: '₹5,800/sq ft', growth: '+10%' },
      { name: 'Indiranagar', avgPrice: '₹7,200/sq ft', growth: '+18%' },
    ],
    priceHistory: [
      { month: 'Jan', price: 4200 },
      { month: 'Feb', price: 4350 },
      { month: 'Mar', price: 4400 },
      { month: 'Apr', price: 4550 },
      { month: 'May', price: 4650 },
      { month: 'Jun', price: 4850 },
    ],
    insights: [
      {
        title: 'Rising Demand',
        description: 'Property demand has increased by 25% in the last quarter',
        icon: 'trending-up',
        color: '#4CAF50',
      },
      {
        title: 'New Developments',
        description: '15 new residential projects launched this month',
        icon: 'business',
        color: '#2196F3',
      },
      {
        title: 'Price Appreciation',
        description: 'Average price appreciation of 8.5% year-over-year',
        icon: 'arrow-up-circle',
        color: '#FF9800',
      },
      {
        title: 'Investment Hotspot',
        description: 'Tech corridor areas showing highest ROI potential',
        icon: 'location',
        color: '#9C27B0',
      },
    ],
  };

  const renderMetricCard = (title, value, change, icon) => (
    <View style={[styles.metricCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
      <View style={styles.metricHeader}>
        <View style={[styles.metricIcon, { backgroundColor: theme.PRIMARY + '20' }]}>
          <Ionicons name={icon} size={24} color={theme.PRIMARY} />
        </View>
        <View style={styles.metricChange}>
          <Ionicons
            name={change > 0 ? 'arrow-up' : 'arrow-down'}
            size={16}
            color={change > 0 ? '#4CAF50' : '#F44336'}
          />
          <Text style={[
            styles.changeText,
            { color: change > 0 ? '#4CAF50' : '#F44336' }
          ]}>
            {Math.abs(change)}%
          </Text>
        </View>
      </View>
      <Text style={[styles.metricValue, { color: theme.TEXT_PRIMARY }]}>
        {value}
      </Text>
      <Text style={[styles.metricTitle, { color: theme.TEXT_SECONDARY }]}>
        {title}
      </Text>
    </View>
  );

  const renderAreaCard = (area, index) => (
    <View key={index} style={[styles.areaCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
      <View style={styles.areaInfo}>
        <Text style={[styles.areaName, { color: theme.TEXT_PRIMARY }]}>
          {area.name}
        </Text>
        <Text style={[styles.areaPrice, { color: theme.TEXT_SECONDARY }]}>
          {area.avgPrice}
        </Text>
      </View>
      <View style={[styles.growthBadge, { backgroundColor: '#4CAF50' + '20' }]}>
        <Text style={[styles.growthText, { color: '#4CAF50' }]}>
          {area.growth}
        </Text>
      </View>
    </View>
  );

  const renderInsightCard = (insight, index) => (
    <View key={index} style={[styles.insightCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
      <View style={[styles.insightIcon, { backgroundColor: insight.color + '20' }]}>
        <Ionicons name={insight.icon} size={24} color={insight.color} />
      </View>
      <View style={styles.insightContent}>
        <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
          {insight.title}
        </Text>
        <Text style={[styles.insightDescription, { color: theme.TEXT_SECONDARY }]}>
          {insight.description}
        </Text>
      </View>
    </View>
  );

  const renderPriceChart = () => (
    <View style={[styles.chartContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
      <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>
        Price Trend (₹/sq ft)
      </Text>
      <View style={styles.chart}>
        {marketData.priceHistory.map((item, index) => (
          <View key={index} style={styles.chartBar}>
            <View
              style={[
                styles.bar,
                {
                  height: (item.price / 5000) * 100,
                  backgroundColor: theme.PRIMARY,
                }
              ]}
            />
            <Text style={[styles.chartLabel, { color: theme.TEXT_SECONDARY }]}>
              {item.month}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Market Analytics</Text>
          <TouchableOpacity onPress={() => router.push('/Analytics/AnalyticsSettings')}>
            <Ionicons name="settings" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Filters */}
        <View style={styles.filtersSection}>
          {/* Location Filter */}
          <Text style={[styles.filterLabel, { color: theme.TEXT_PRIMARY }]}>
            Location
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.filterContainer}
          >
            {locations.map((location) => (
              <TouchableOpacity
                key={location.id}
                style={[
                  styles.filterButton,
                  {
                    backgroundColor: selectedLocation === location.id ? theme.PRIMARY : theme.CARD_BACKGROUND,
                  }
                ]}
                onPress={() => setSelectedLocation(location.id)}
              >
                <Text style={[
                  styles.filterText,
                  {
                    color: selectedLocation === location.id ? '#fff' : theme.TEXT_SECONDARY,
                  }
                ]}>
                  {location.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Period Filter */}
          <Text style={[styles.filterLabel, { color: theme.TEXT_PRIMARY }]}>
            Time Period
          </Text>
          <View style={styles.periodContainer}>
            {periods.map((period) => (
              <TouchableOpacity
                key={period.id}
                style={[
                  styles.periodButton,
                  {
                    backgroundColor: selectedPeriod === period.id ? theme.PRIMARY : theme.CARD_BACKGROUND,
                  }
                ]}
                onPress={() => setSelectedPeriod(period.id)}
              >
                <Text style={[
                  styles.periodText,
                  {
                    color: selectedPeriod === period.id ? '#fff' : theme.TEXT_SECONDARY,
                  }
                ]}>
                  {period.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Key Metrics */}
        <View style={styles.metricsSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Key Metrics
          </Text>
          <View style={styles.metricsGrid}>
            {renderMetricCard('Price Index', '4,850', 8.5, 'trending-up')}
            {renderMetricCard('Total Listings', '12.4K', 12.3, 'list')}
            {renderMetricCard('Properties Sold', '156', -5.2, 'checkmark-circle')}
            {renderMetricCard('Avg. Days on Market', '45', -8.1, 'time')}
          </View>
        </View>

        {/* Price Chart */}
        <View style={styles.chartSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Price Trends
          </Text>
          {renderPriceChart()}
        </View>

        {/* Average Prices */}
        <View style={styles.pricesSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Average Prices
          </Text>
          <View style={styles.pricesGrid}>
            <View style={[styles.priceCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
              <Ionicons name="home" size={24} color="#4CAF50" />
              <Text style={[styles.priceType, { color: theme.TEXT_SECONDARY }]}>
                Residential
              </Text>
              <Text style={[styles.priceValue, { color: theme.TEXT_PRIMARY }]}>
                {marketData.averagePrice.residential}
              </Text>
            </View>
            <View style={[styles.priceCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
              <Ionicons name="business" size={24} color="#2196F3" />
              <Text style={[styles.priceType, { color: theme.TEXT_SECONDARY }]}>
                Commercial
              </Text>
              <Text style={[styles.priceValue, { color: theme.TEXT_PRIMARY }]}>
                {marketData.averagePrice.commercial}
              </Text>
            </View>
            <View style={[styles.priceCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
              <Ionicons name="map" size={24} color="#FF9800" />
              <Text style={[styles.priceType, { color: theme.TEXT_SECONDARY }]}>
                Plots
              </Text>
              <Text style={[styles.priceValue, { color: theme.TEXT_PRIMARY }]}>
                {marketData.averagePrice.plots}
              </Text>
            </View>
          </View>
        </View>

        {/* Top Areas */}
        <View style={styles.areasSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Top Performing Areas
          </Text>
          <View style={styles.areasList}>
            {marketData.topAreas.map(renderAreaCard)}
          </View>
        </View>

        {/* Market Insights */}
        <View style={styles.insightsSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Market Insights
          </Text>
          <View style={styles.insightsList}>
            {marketData.insights.map(renderInsightCard)}
          </View>
        </View>

        <View style={{ height: 40 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  filtersSection: {
    padding: 20,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 16,
  },
  filterContainer: {
    marginBottom: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
  },
  periodContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  periodText: {
    fontSize: 14,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  metricsSection: {
    marginBottom: 24,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  metricCard: {
    width: (width - 60) / 2,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metricChange: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeText: {
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 2,
  },
  metricValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 12,
  },
  chartSection: {
    marginBottom: 24,
  },
  chartContainer: {
    marginHorizontal: 20,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  chart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 120,
  },
  chartBar: {
    alignItems: 'center',
    flex: 1,
  },
  bar: {
    width: 20,
    borderRadius: 4,
    marginBottom: 8,
  },
  chartLabel: {
    fontSize: 12,
  },
  pricesSection: {
    marginBottom: 24,
  },
  pricesGrid: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  priceCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  priceType: {
    fontSize: 12,
    marginTop: 8,
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  areasSection: {
    marginBottom: 24,
  },
  areasList: {
    paddingHorizontal: 20,
  },
  areaCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  areaInfo: {
    flex: 1,
  },
  areaName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  areaPrice: {
    fontSize: 14,
  },
  growthBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  growthText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  insightsSection: {
    marginBottom: 24,
  },
  insightsList: {
    paddingHorizontal: 20,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  insightIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  insightDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default MarketAnalytics;
