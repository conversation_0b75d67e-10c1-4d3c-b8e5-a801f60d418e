import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';

const NotificationsList = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState('all');

  // Mock notifications data
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: 'payment',
      title: 'Payment Received',
      message: 'You received ₹5,000 from <PERSON> for land consultation',
      time: '2 hours ago',
      read: false,
      icon: 'card',
      color: '#4CAF50',
    },
    {
      id: 2,
      type: 'booking',
      title: 'New Booking Request',
      message: 'Elite Builders wants to book your services for a residential project',
      time: '4 hours ago',
      read: false,
      icon: 'calendar',
      color: '#2196F3',
    },
    {
      id: 3,
      type: 'message',
      title: 'New Message',
      message: 'You have a new message from <PERSON> about property inquiry',
      time: '6 hours ago',
      read: true,
      icon: 'chatbubble',
      color: '#FF9800',
    },
    {
      id: 4,
      type: 'system',
      title: 'Profile Verification',
      message: 'Your contractor profile has been successfully verified',
      time: '1 day ago',
      read: true,
      icon: 'checkmark-circle',
      color: '#9C27B0',
    },
    {
      id: 5,
      type: 'promotion',
      title: 'Special Offer',
      message: 'Get 20% off on premium listing for this month only!',
      time: '2 days ago',
      read: true,
      icon: 'gift',
      color: '#E91E63',
    },
    {
      id: 6,
      type: 'reminder',
      title: 'Project Deadline',
      message: 'Reminder: Your project with ABC Construction is due tomorrow',
      time: '3 days ago',
      read: true,
      icon: 'alarm',
      color: '#FF5722',
    },
  ]);

  const tabs = [
    { id: 'all', name: 'All', count: notifications.length },
    { id: 'unread', name: 'Unread', count: notifications.filter(n => !n.read).length },
    { id: 'payment', name: 'Payments', count: notifications.filter(n => n.type === 'payment').length },
    { id: 'booking', name: 'Bookings', count: notifications.filter(n => n.type === 'booking').length },
  ];

  const filteredNotifications = notifications.filter(notification => {
    if (selectedTab === 'all') return true;
    if (selectedTab === 'unread') return !notification.read;
    return notification.type === selectedTab;
  });

  const markAsRead = (id) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const deleteNotification = (id) => {
    Alert.alert(
      'Delete Notification',
      'Are you sure you want to delete this notification?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setNotifications(prev => prev.filter(n => n.id !== id));
          },
        },
      ]
    );
  };

  const handleNotificationPress = (notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }

    // Navigate based on notification type
    switch (notification.type) {
      case 'payment':
        router.push('/Payment/WalletScreen');
        break;
      case 'booking':
        router.push('/Bookings/BookingDetails');
        break;
      case 'message':
        router.push('/(tabs)/Chats');
        break;
      default:
        // Show notification details
        break;
    }
  };

  const renderNotification = (notification) => (
    <TouchableOpacity
      key={notification.id}
      style={[
        styles.notificationCard,
        {
          backgroundColor: theme.CARD_BACKGROUND,
          borderLeftColor: notification.read ? 'transparent' : theme.PRIMARY,
        }
      ]}
      onPress={() => handleNotificationPress(notification)}
    >
      <View style={styles.notificationContent}>
        <View style={[styles.iconContainer, { backgroundColor: notification.color + '20' }]}>
          <Ionicons name={notification.icon} size={24} color={notification.color} />
        </View>
        
        <View style={styles.textContainer}>
          <View style={styles.titleRow}>
            <Text style={[
              styles.notificationTitle,
              {
                color: theme.TEXT_PRIMARY,
                fontWeight: notification.read ? '600' : 'bold',
              }
            ]}>
              {notification.title}
            </Text>
            {!notification.read && (
              <View style={[styles.unreadDot, { backgroundColor: theme.PRIMARY }]} />
            )}
          </View>
          
          <Text style={[
            styles.notificationMessage,
            {
              color: theme.TEXT_SECONDARY,
              fontWeight: notification.read ? 'normal' : '500',
            }
          ]}>
            {notification.message}
          </Text>
          
          <Text style={[styles.notificationTime, { color: theme.TEXT_SECONDARY }]}>
            {notification.time}
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => deleteNotification(notification.id)}
      >
        <Ionicons name="trash-outline" size={20} color={theme.TEXT_SECONDARY} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          <TouchableOpacity onPress={markAllAsRead}>
            <Text style={styles.markAllRead}>Mark all read</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.tabsContainer}
        contentContainerStyle={styles.tabsContent}
      >
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tab,
              {
                backgroundColor: selectedTab === tab.id ? theme.PRIMARY : theme.CARD_BACKGROUND,
              }
            ]}
            onPress={() => setSelectedTab(tab.id)}
          >
            <Text
              style={[
                styles.tabText,
                {
                  color: selectedTab === tab.id ? '#fff' : theme.TEXT_SECONDARY,
                }
              ]}
            >
              {tab.name}
            </Text>
            {tab.count > 0 && (
              <View style={[
                styles.tabBadge,
                {
                  backgroundColor: selectedTab === tab.id ? 'rgba(255,255,255,0.3)' : theme.PRIMARY,
                }
              ]}>
                <Text style={[
                  styles.tabBadgeText,
                  {
                    color: selectedTab === tab.id ? '#fff' : '#fff',
                  }
                ]}>
                  {tab.count}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Notifications List */}
      <ScrollView
        style={styles.notificationsList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.notificationsContent}
      >
        {filteredNotifications.length > 0 ? (
          filteredNotifications.map(renderNotification)
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="notifications-off" size={64} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
              No notifications found
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  markAllRead: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  tabsContainer: {
    marginBottom: 10,
  },
  tabsContent: {
    paddingHorizontal: 20,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tabBadge: {
    marginLeft: 6,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  notificationsList: {
    flex: 1,
  },
  notificationsContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  notificationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  notificationContent: {
    flexDirection: 'row',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  notificationMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
  },
  deleteButton: {
    padding: 8,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
});

export default NotificationsList;
