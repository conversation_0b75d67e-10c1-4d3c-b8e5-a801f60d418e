import React, { useState } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    ScrollView,
    ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles';

const BrokerDetailsStep = ({
    formik,
    theme,
    setStep,
    isCreating,
    isUpdating,
}) => {
    const isSubmitting = isCreating || isUpdating;
    const [serviceAreaInput, setServiceAreaInput] = useState('');
    const handleAddServiceArea = () => {
        const value = serviceAreaInput.trim();
        if (
            value &&
            !formik.values.serviceAreas.includes(value) &&
            formik.values.serviceAreas.length < 10
        ) {
            formik.setFieldValue('serviceAreas', [
                ...formik.values.serviceAreas,
                value,
            ]);
            setServiceAreaInput('');
        }
    };

    const handleRemoveServiceArea = (area) => {
        formik.setFieldValue(
            'serviceAreas',
            formik.values.serviceAreas.filter((a) => a !== area)
        );
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Broker Details
            </Text>
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                    formik.touched.experience && formik.errors.experience
                        ? styles.inputError
                        : null,
                ]}
            >
                <Ionicons
                    name="briefcase-outline"
                    size={22}
                    color={
                        formik.touched.experience && formik.errors.experience
                            ? 'red'
                            : theme.PRIMARY
                    }
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Years of Experience"
                    value={formik.values.experience?.toString() || ''}
                    onChangeText={(text) => {
                        const numericValue = text.replace(/[^0-9]/g, '');
                        formik.setFieldValue('experience', numericValue);
                    }}
                    onBlur={formik.handleBlur('experience')}
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    keyboardType="numeric"
                    accessibilityLabel="Enter years of experience"
                    testID="experience-input"
                />
                {formik.values.experience !== 0 && (
                    <TouchableOpacity
                        onPress={() => formik.setFieldValue('experience', '')}
                        style={styles.clearButton}
                    >
                        <Ionicons name="close-circle" size={20} color="#999" />
                    </TouchableOpacity>
                )}
            </View>
            {formik.touched.experience && formik.errors.experience && (
                <Text style={styles.errorText}>{formik.errors.experience}</Text>
            )}
            <View
                style={[
                    styles.inputContainer,
                    styles.serviceAreaContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                    formik.touched.serviceAreas && formik.errors.serviceAreas
                        ? styles.inputError
                        : null,
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={
                        formik.touched.serviceAreas &&
                        formik.errors.serviceAreas
                            ? 'red'
                            : theme.PRIMARY
                    }
                    style={styles.inputIcon}
                />
                <View style={{ flex: 1 }}>
                    <View
                        style={{ flexDirection: 'row', alignItems: 'center' }}
                    >
                        <TextInput
                            placeholder="Add Service Area"
                            value={serviceAreaInput}
                            onChangeText={setServiceAreaInput}
                            onSubmitEditing={handleAddServiceArea}
                            placeholderTextColor={theme.TEXT_PLACEHOLDER}
                            style={[
                                styles.input,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            editable={formik.values.serviceAreas.length < 10}
                            accessibilityLabel="Enter a service area"
                            testID="serviceAreas-input"
                            returnKeyType="done"
                        />
                        <TouchableOpacity
                            onPress={handleAddServiceArea}
                            disabled={
                                !serviceAreaInput.trim() ||
                                formik.values.serviceAreas.length >= 10
                            }
                            style={{ padding: 8 }}
                            accessibilityLabel="Add service area"
                        >
                            <Ionicons
                                name="add-circle"
                                size={24}
                                color={
                                    !serviceAreaInput.trim() ||
                                    formik.values.serviceAreas.length >= 10
                                        ? '#ccc'
                                        : theme.PRIMARY
                                }
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    {formik.values.serviceAreas.map((area, idx) => (
                        <View
                            key={area + idx}
                            style={[
                                styles.serviceAreaTag,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                            ]}
                        >
                            <Text
                                style={{
                                    color: theme.TEXT_PRIMARY,
                                    marginRight: 4,
                                }}
                            >
                                {area}
                            </Text>
                            <TouchableOpacity
                                onPress={() => handleRemoveServiceArea(area)}
                                accessibilityLabel={`Remove ${area}`}
                            >
                                <Ionicons
                                    name="close-circle"
                                    size={18}
                                    color="#999"
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </ScrollView>
            </View>
            {formik.touched.serviceAreas && formik.errors.serviceAreas && (
                <Text style={styles.errorText}>
                    {formik.errors.serviceAreas}
                </Text>
            )}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.submitButton,
                        { flex: 1, marginRight: 8, borderColor: theme.PRIMARY },
                    ]}
                    onPress={() => setStep('pan')}
                    accessibilityLabel="Go back to PAN details"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.WHITE, theme.GRAY_LIGHT]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Back
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.submitButton,
                        { flex: 1, marginLeft: 8, borderColor: theme.ACCENT },
                    ]}
                    onPress={async () => {
                        const errors = await formik.validateForm();
                        const fields = ['experience', 'serviceAreas'];
                        if (!fields.some((field) => errors[field])) {
                            formik.handleSubmit();
                        } else {
                            fields.forEach((field) =>
                                formik.setFieldTouched(field, true)
                            );
                        }
                    }}
                    accessibilityLabel={
                        formik.initialValues.brokerId
                            ? 'Update broker application'
                            : 'Submit broker application'
                    }
                    accessibilityRole="button"
                    testID="submit-button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        {isSubmitting ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator
                                    color={theme.WHITE}
                                    size="small"
                                    style={{ marginRight: 8 }}
                                />
                                <Text
                                    style={[
                                        styles.submitButtonText,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Submitting...
                                </Text>
                            </View>
                        ) : (
                            <Text
                                style={[
                                    styles.submitButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                {formik.initialValues.brokerId
                                    ? 'Update'
                                    : 'Submit'}
                            </Text>
                        )}
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default BrokerDetailsStep;
