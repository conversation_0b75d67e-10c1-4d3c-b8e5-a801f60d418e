import { useContext } from 'react';
import { View, Text, Image, StyleSheet, StatusBar } from 'react-native';
import { ThemeContext } from '../../../context/ThemeContext';
import {
    createStandardStyles,
    SPACING,
    BORDER_RADIUS,
    SHADOW_PATTERNS,
} from '../Shared/StandardStyles';
import BackButton from '../Shared/BackButton';

export default function ChatScreen({ route }) {
    const { user } = route.params;
    const { theme, isDarkMode } = useContext(ThemeContext);
    const standardStyles = createStandardStyles(theme);

    return (
        <View style={[standardStyles.container]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />

            <BackButton color={theme.PRIMARY} />

            <View
                style={[
                    styles.header,
                    {
                        backgroundColor: theme.CARD,
                        borderBottomColor: theme.INPUT_BORDER,
                        ...SHADOW_PATTERNS.LIGHT,
                    },
                ]}
            >
                <Image
                    source={user.profile}
                    style={[
                        styles.profileImage,
                        {
                            borderColor: theme.PRIMARY + '30',
                        },
                    ]}
                />
                <Text
                    style={[standardStyles.sectionTitle, { marginBottom: 0 }]}
                >
                    {user.name}
                </Text>
            </View>

            {/* Chat content area */}
            <View style={styles.chatPlaceholder}>
                <Text style={[standardStyles.caption, { textAlign: 'center' }]}>
                    Start chatting with {user.name}...
                </Text>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: SPACING.MD,
        borderBottomWidth: 1,
        marginHorizontal: SPACING.MD,
        marginTop: SPACING.SM,
        borderRadius: BORDER_RADIUS.MD,
    },
    profileImage: {
        width: 50,
        height: 50,
        borderRadius: 25,
        marginRight: SPACING.MD,
        borderWidth: 2,
    },
    chatPlaceholder: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: SPACING.LG,
    },
});
