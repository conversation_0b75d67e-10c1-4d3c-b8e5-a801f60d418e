import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';

export default function ChatScreen({ route }) {
    const { user } = route.params;

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Image source={user.profile} style={styles.profileImage} />
                <Text style={styles.name}>{user.name}</Text>
            </View>

            {/* You can now add chat bubbles and message input below this */}
            <View style={styles.chatPlaceholder}>
                <Text style={{ color: '#aaa' }}>
                    Start chatting with {user.name}...
                </Text>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: '#fff' },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 0.5,
        borderColor: '#ccc',
    },
    profileImage: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginRight: 12,
    },
    name: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    chatPlaceholder: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
