import { View, Text, FlatList, TouchableOpacity, Animated } from 'react-native';
import React, { useRef, useContext } from 'react';
import Card from './Card';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import { ThemeContext } from '../../../context/ThemeContext';

export default function CategorySection({
    title,
    data,
    onItemPress,
    viewAllRoute,
}) {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const fadeAnim = useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
        }).start();
    }, [fadeAnim]);

    const handleViewAll = () => {
        if (viewAllRoute) {
            router.push(viewAllRoute);
        }
    };
    return (
        <Animated.View
            style={{ opacity: fadeAnim, marginTop: 8, paddingVertical: 4 }}
        >
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingHorizontal: 16,
                    marginBottom: 12,
                }}
            >
                <Text
                    style={{
                        fontSize: 18,
                        fontWeight: 'bold',
                        color: theme.PRIMARY,
                    }}
                >
                    {title}
                </Text>
                {viewAllRoute && (
                    <TouchableOpacity
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            backgroundColor: theme.PRIMARY + '18',
                            paddingHorizontal: 12,
                            paddingVertical: 6,
                            borderRadius: 999,
                        }}
                        onPress={handleViewAll}
                        accessibilityLabel={`View all ${title}`}
                    >
                        <Text
                            style={{
                                fontSize: 14,
                                fontWeight: '600',
                                color: theme.PRIMARY,
                                marginRight: 4,
                            }}
                        >
                            View All
                        </Text>
                        <Ionicons
                            name="chevron-forward"
                            size={16}
                            color={theme.PRIMARY}
                        />
                    </TouchableOpacity>
                )}
            </View>
            <FlatList
                data={data}
                keyExtractor={(item) =>
                    item.id?.toString?.() ||
                    item._id?.toString?.() ||
                    Math.random().toString()
                }
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingHorizontal: 16 }}
                renderItem={({ item }) => (
                    <Card
                        {...item}
                        onPress={() => onItemPress(item)}
                        onInterestedPress={item.onInterestedPress}
                        accessibilityLabel={`View details for ${item.title || item.name}`}
                    />
                )}
                ListEmptyComponent={
                    <View style={{ padding: 24 }}>
                        <Text
                            style={{
                                color: theme.TEXT_SECONDARY,
                                fontSize: 15,
                            }}
                        >
                            No items to display.
                        </Text>
                    </View>
                }
            />
        </Animated.View>
    );
}
