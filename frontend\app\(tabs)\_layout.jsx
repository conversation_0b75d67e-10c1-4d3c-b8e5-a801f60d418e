import React, { useState, useContext } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Tabs } from 'expo-router';
import { Ionicons, AntDesign } from '@expo/vector-icons';
import FABModal from '../Components/Home/FabModal';
import '../../global.css';
import { ThemeContext } from '../../context/ThemeContext';

export default function TabLayout() {
    const { theme } = useContext(ThemeContext);
    const [modalVisible, setModalVisible] = useState(false);

    return (
        <View style={{ flex: 1 }}>
            <FABModal
                modalVisible={modalVisible}
                setModalVisible={setModalVisible}
            />
            <Tabs
                screenOptions={{
                    tabBarActiveTintColor: theme.PRIMARY,
                    tabBarInactiveTintColor: theme.TEXT_SECONDARY,
                    tabBarStyle: {
                        backgroundColor: theme.CARD_BACKGROUND,
                        borderTopColor: theme.BORDER,
                        borderTopWidth: 1,
                        height: 60,
                        paddingBottom: 8,
                        paddingTop: 8,
                    },
                    tabBarLabelStyle: {
                        fontSize: 12,
                        fontWeight: '600',
                    },
                    headerShown: false,
                    tabBarHideOnKeyboard: true,
                }}
            >
                <Tabs.Screen
                    name="Home"
                    options={{
                        title: 'Home',
                        tabBarIcon: ({ color, focused }) => (
                            <Ionicons
                                name={focused ? 'home' : 'home-outline'}
                                size={28}
                                color={color}
                                style={
                                    focused
                                        ? { transform: [{ scale: 1.15 }] }
                                        : {}
                                }
                            />
                        ),
                    }}
                />

                <Tabs.Screen
                    name="Listings"
                    options={{
                        title: 'Listings',
                        tabBarIcon: ({ color, focused }) => (
                            <Ionicons
                                name={focused ? 'bookmark' : 'bookmark-outline'}
                                size={28}
                                color={color}
                                style={
                                    focused
                                        ? { transform: [{ scale: 1.15 }] }
                                        : {}
                                }
                            />
                        ),
                    }}
                />

                <Tabs.Screen
                    name="Add"
                    options={{
                        title: '',
                        tabBarButton: (props) => (
                            <View
                                style={{
                                    top: -2,
                                    left: 9,
                                    right: 9,
                                    backgroundColor: theme.BORDER,
                                    borderWidth: 2,
                                    borderColor: theme.PRIMARY,
                                    width: 48,
                                    height: 48,
                                    borderRadius: 24,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    shadowColor: theme.SHADOW,
                                    shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.25,
                                    shadowRadius: 3.84,
                                    elevation: 5,
                                }}
                            >
                                <TouchableOpacity
                                    activeOpacity={0.8}
                                    onPress={() => setModalVisible(true)}
                                >
                                    <AntDesign
                                        name="pluscircle"
                                        size={42}
                                        color={theme.PRIMARY}
                                    />
                                </TouchableOpacity>
                            </View>
                        ),
                    }}
                />

                <Tabs.Screen
                    name="Chats"
                    options={{
                        title: 'Chats',
                        tabBarIcon: ({ color, focused }) => (
                            <Ionicons
                                name={
                                    focused
                                        ? 'chatbubble-ellipses'
                                        : 'chatbubble-ellipses-outline'
                                }
                                size={28}
                                color={color}
                                style={
                                    focused
                                        ? { transform: [{ scale: 1.15 }] }
                                        : {}
                                }
                            />
                        ),
                    }}
                />

                <Tabs.Screen
                    name="More"
                    options={{
                        title: 'More',
                        tabBarIcon: ({ color, focused }) => (
                            <Ionicons
                                name={focused ? 'grid' : 'grid-outline'}
                                size={28}
                                color={color}
                                style={
                                    focused
                                        ? { transform: [{ scale: 1.15 }] }
                                        : {}
                                }
                            />
                        ),
                    }}
                />
            </Tabs>
        </View>
    );
}
