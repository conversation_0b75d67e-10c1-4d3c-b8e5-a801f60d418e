import axios from 'axios';
import { API_BASE_URL } from '../index';

const adminApi = axios.create({
  baseURL: `${API_BASE_URL}/api/admin`,
});

// Add auth token to requests
adminApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Dashboard Analytics
export const getDashboardStats = async () => {
  const { data } = await adminApi.get('/dashboard/stats');
  return data;
};

export const getRevenueAnalytics = async (timeframe = '30d') => {
  const { data } = await adminApi.get('/dashboard/revenue', {
    params: { timeframe },
  });
  return data;
};

export const getUserGrowthAnalytics = async (timeframe = '30d') => {
  const { data } = await adminApi.get('/dashboard/user-growth', {
    params: { timeframe },
  });
  return data;
};

export const getTransactionAnalytics = async (timeframe = '30d') => {
  const { data } = await adminApi.get('/dashboard/transactions', {
    params: { timeframe },
  });
  return data;
};

// User Management
export const getAllUsers = async (page = 1, limit = 20, filters = {}) => {
  const { data } = await adminApi.get('/users', {
    params: { page, limit, ...filters },
  });
  return data;
};

export const getUserDetails = async (userId) => {
  const { data } = await adminApi.get(`/users/${userId}`);
  return data;
};

export const updateUserStatus = async (userId, status, reason = '') => {
  const { data } = await adminApi.put(`/users/${userId}/status`, {
    status, // 'active', 'suspended', 'banned'
    reason,
  });
  return data;
};

export const verifyUser = async (userId, verificationType, status) => {
  const { data } = await adminApi.put(`/users/${userId}/verify`, {
    verificationType, // 'email', 'phone', 'identity', 'address'
    status, // 'verified', 'rejected'
  });
  return data;
};

export const deleteUser = async (userId, reason) => {
  const { data } = await adminApi.delete(`/users/${userId}`, {
    data: { reason },
  });
  return data;
};

// Broker Management
export const getAllBrokers = async (page = 1, limit = 20, filters = {}) => {
  const { data } = await adminApi.get('/brokers', {
    params: { page, limit, ...filters },
  });
  return data;
};

export const getBrokerApplications = async (status = 'pending') => {
  const { data } = await adminApi.get('/brokers/applications', {
    params: { status },
  });
  return data;
};

export const approveBrokerApplication = async (applicationId, approvalData) => {
  const { data } = await adminApi.post(`/brokers/applications/${applicationId}/approve`, approvalData);
  return data;
};

export const rejectBrokerApplication = async (applicationId, reason) => {
  const { data } = await adminApi.post(`/brokers/applications/${applicationId}/reject`, {
    reason,
  });
  return data;
};

export const updateBrokerCommission = async (brokerId, commissionRate) => {
  const { data } = await adminApi.put(`/brokers/${brokerId}/commission`, {
    commissionRate,
  });
  return data;
};

// Contractor Management
export const getAllContractors = async (page = 1, limit = 20, filters = {}) => {
  const { data } = await adminApi.get('/contractors', {
    params: { page, limit, ...filters },
  });
  return data;
};

export const getContractorApplications = async (status = 'pending') => {
  const { data } = await adminApi.get('/contractors/applications', {
    params: { status },
  });
  return data;
};

export const approveContractorApplication = async (applicationId, approvalData) => {
  const { data } = await adminApi.post(`/contractors/applications/${applicationId}/approve`, approvalData);
  return data;
};

export const rejectContractorApplication = async (applicationId, reason) => {
  const { data } = await adminApi.post(`/contractors/applications/${applicationId}/reject`, {
    reason,
  });
  return data;
};

export const updateContractorRating = async (contractorId, rating, reason) => {
  const { data } = await adminApi.put(`/contractors/${contractorId}/rating`, {
    rating,
    reason,
  });
  return data;
};

// Land Management
export const getAllLands = async (page = 1, limit = 20, filters = {}) => {
  const { data } = await adminApi.get('/lands', {
    params: { page, limit, ...filters },
  });
  return data;
};

export const getLandVerificationQueue = async () => {
  const { data } = await adminApi.get('/lands/verification-queue');
  return data;
};

export const verifyLandListing = async (landId, verificationData) => {
  const { data } = await adminApi.post(`/lands/${landId}/verify`, verificationData);
  return data;
};

export const rejectLandListing = async (landId, reason) => {
  const { data } = await adminApi.post(`/lands/${landId}/reject`, {
    reason,
  });
  return data;
};

export const flagLandListing = async (landId, flagReason) => {
  const { data } = await adminApi.post(`/lands/${landId}/flag`, {
    reason: flagReason,
  });
  return data;
};

// Transaction Management
export const getAllTransactions = async (page = 1, limit = 20, filters = {}) => {
  const { data } = await adminApi.get('/transactions', {
    params: { page, limit, ...filters },
  });
  return data;
};

export const getTransactionDetails = async (transactionId) => {
  const { data } = await adminApi.get(`/transactions/${transactionId}`);
  return data;
};

export const refundTransaction = async (transactionId, refundData) => {
  const { data } = await adminApi.post(`/transactions/${transactionId}/refund`, refundData);
  return data;
};

export const disputeTransaction = async (transactionId, disputeData) => {
  const { data } = await adminApi.post(`/transactions/${transactionId}/dispute`, disputeData);
  return data;
};

// Support Ticket Management
export const getAllTickets = async (page = 1, limit = 20, filters = {}) => {
  const { data } = await adminApi.get('/tickets', {
    params: { page, limit, ...filters },
  });
  return data;
};

export const getTicketDetails = async (ticketId) => {
  const { data } = await adminApi.get(`/tickets/${ticketId}`);
  return data;
};

export const updateTicketStatus = async (ticketId, status, response = '') => {
  const { data } = await adminApi.put(`/tickets/${ticketId}/status`, {
    status, // 'open', 'in_progress', 'resolved', 'closed'
    response,
  });
  return data;
};

export const assignTicket = async (ticketId, assigneeId) => {
  const { data } = await adminApi.put(`/tickets/${ticketId}/assign`, {
    assigneeId,
  });
  return data;
};

export const addTicketResponse = async (ticketId, response, isInternal = false) => {
  const { data } = await adminApi.post(`/tickets/${ticketId}/responses`, {
    response,
    isInternal,
  });
  return data;
};

// Content Management
export const getReportedContent = async (contentType = 'all') => {
  const { data } = await adminApi.get('/content/reported', {
    params: { contentType }, // 'lands', 'reviews', 'messages', 'all'
  });
  return data;
};

export const moderateContent = async (contentId, contentType, action, reason = '') => {
  const { data } = await adminApi.post('/content/moderate', {
    contentId,
    contentType,
    action, // 'approve', 'remove', 'flag'
    reason,
  });
  return data;
};

export const banContent = async (contentId, contentType, reason) => {
  const { data } = await adminApi.post('/content/ban', {
    contentId,
    contentType,
    reason,
  });
  return data;
};

// System Settings
export const getSystemSettings = async () => {
  const { data } = await adminApi.get('/settings');
  return data;
};

export const updateSystemSettings = async (settings) => {
  const { data } = await adminApi.put('/settings', settings);
  return data;
};

export const getCommissionSettings = async () => {
  const { data } = await adminApi.get('/settings/commission');
  return data;
};

export const updateCommissionSettings = async (commissionSettings) => {
  const { data } = await adminApi.put('/settings/commission', commissionSettings);
  return data;
};

// Notifications Management
export const sendBulkNotification = async (notificationData) => {
  const { data } = await adminApi.post('/notifications/bulk', notificationData);
  return data;
};

export const getNotificationTemplates = async () => {
  const { data } = await adminApi.get('/notifications/templates');
  return data;
};

export const createNotificationTemplate = async (templateData) => {
  const { data } = await adminApi.post('/notifications/templates', templateData);
  return data;
};

export const updateNotificationTemplate = async (templateId, templateData) => {
  const { data } = await adminApi.put(`/notifications/templates/${templateId}`, templateData);
  return data;
};

// Audit Logs
export const getAuditLogs = async (page = 1, limit = 50, filters = {}) => {
  const { data } = await adminApi.get('/audit-logs', {
    params: { page, limit, ...filters },
  });
  return data;
};

export const exportAuditLogs = async (filters = {}) => {
  const { data } = await adminApi.get('/audit-logs/export', {
    params: filters,
    responseType: 'blob',
  });
  return data;
};

// Reports and Analytics
export const generateReport = async (reportType, parameters = {}) => {
  const { data } = await adminApi.post('/reports/generate', {
    reportType, // 'user_activity', 'revenue', 'transactions', 'land_listings'
    parameters,
  });
  return data;
};

export const getReportHistory = async () => {
  const { data } = await adminApi.get('/reports/history');
  return data;
};

export const downloadReport = async (reportId) => {
  const { data } = await adminApi.get(`/reports/${reportId}/download`, {
    responseType: 'blob',
  });
  return data;
};

// Admin User Management
export const getAdminUsers = async () => {
  const { data } = await adminApi.get('/admin-users');
  return data;
};

export const createAdminUser = async (adminData) => {
  const { data } = await adminApi.post('/admin-users', adminData);
  return data;
};

export const updateAdminUser = async (adminId, updateData) => {
  const { data } = await adminApi.put(`/admin-users/${adminId}`, updateData);
  return data;
};

export const updateAdminPermissions = async (adminId, permissions) => {
  const { data } = await adminApi.put(`/admin-users/${adminId}/permissions`, {
    permissions,
  });
  return data;
};

export const deactivateAdminUser = async (adminId) => {
  const { data } = await adminApi.put(`/admin-users/${adminId}/deactivate`);
  return data;
};

export default adminApi;
