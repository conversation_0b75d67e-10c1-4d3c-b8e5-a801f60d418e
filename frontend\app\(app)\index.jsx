import React, { useContext, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  RefreshControl
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { fetchUserProfile } from '../../api/user/userApi';

export default function HomeScreen() {
  const { theme, isDarkMode } = useContext(ThemeContext);
  const { user, isAuthenticated } = useContext(AuthContext);
  const router = useRouter();
  const queryClient = useQueryClient();
  
  const userRole = user?.role?.toLowerCase() || 'user';
  
  const { 
    data: userProfile, 
    isLoading, 
    refetch 
  } = useQuery({
    queryKey: ['userProfile'],
    queryFn: fetchUserProfile,
    enabled: isAuthenticated,
  });
  
  const [refreshing, setRefreshing] = React.useState(false);
  
  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);
  
  // Render different dashboard based on user role
  const renderDashboard = () => {
    switch(userRole) {
      case 'admin':
        return renderAdminDashboard();
      case 'broker':
        return renderBrokerDashboard();
      case 'contractor':
        return renderContractorDashboard();
      default:
        return renderUserDashboard();
    }
  };
  
  const renderUserDashboard = () => (
    <>
      <View style={styles.statsContainer}>
        <View style={[styles.statCard, { backgroundColor: theme.CARD }]}>
          <Ionicons name="business" size={24} color={theme.PRIMARY} />
          <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>0</Text>
          <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>My Properties</Text>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: theme.CARD }]}>
          <Ionicons name="construct" size={24} color={theme.PRIMARY} />
          <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>0</Text>
          <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Active Projects</Text>
        </View>
      </View>
      
      <View style={styles.actionsContainer