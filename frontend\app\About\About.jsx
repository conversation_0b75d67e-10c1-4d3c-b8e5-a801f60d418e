import React, { useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Image,
  Linking,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

const About = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();

  const teamMembers = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      role: 'Founder & CEO',
      image: 'https://via.placeholder.com/100',
      description: '15+ years in real estate and construction industry',
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      role: 'CTO',
      image: 'https://via.placeholder.com/100',
      description: 'Tech expert with passion for innovative solutions',
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      role: 'Head of Operations',
      image: 'https://via.placeholder.com/100',
      description: 'Operations specialist ensuring smooth user experience',
    },
  ];

  const features = [
    {
      icon: 'home',
      title: 'Property Listings',
      description: 'Browse and list properties with detailed information and photos',
    },
    {
      icon: 'hammer',
      title: 'Find Contractors',
      description: 'Connect with verified contractors and service providers',
    },
    {
      icon: 'card',
      title: 'Secure Payments',
      description: 'Safe and secure payment processing for all transactions',
    },
    {
      icon: 'map',
      title: 'Location Services',
      description: 'Interactive maps and location-based property search',
    },
    {
      icon: 'chatbubble',
      title: 'Real-time Chat',
      description: 'Communicate directly with property owners and contractors',
    },
    {
      icon: 'sparkles',
      title: 'AI Assistant',
      description: 'Get intelligent recommendations and market insights',
    },
  ];

  const socialLinks = [
    {
      platform: 'Website',
      icon: 'globe',
      url: 'https://buildconnect.com',
      color: '#4CAF50',
    },
    {
      platform: 'Facebook',
      icon: 'logo-facebook',
      url: 'https://facebook.com/buildconnect',
      color: '#1877F2',
    },
    {
      platform: 'Twitter',
      icon: 'logo-twitter',
      url: 'https://twitter.com/buildconnect',
      color: '#1DA1F2',
    },
    {
      platform: 'LinkedIn',
      icon: 'logo-linkedin',
      url: 'https://linkedin.com/company/buildconnect',
      color: '#0A66C2',
    },
    {
      platform: 'Instagram',
      icon: 'logo-instagram',
      url: 'https://instagram.com/buildconnect',
      color: '#E4405F',
    },
  ];

  const handleSocialPress = (url) => {
    Linking.openURL(url);
  };

  const renderTeamMember = (member) => (
    <View key={member.id} style={[styles.teamCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
      <Image source={{ uri: member.image }} style={styles.memberImage} />
      <Text style={[styles.memberName, { color: theme.TEXT_PRIMARY }]}>
        {member.name}
      </Text>
      <Text style={[styles.memberRole, { color: theme.PRIMARY }]}>
        {member.role}
      </Text>
      <Text style={[styles.memberDescription, { color: theme.TEXT_SECONDARY }]}>
        {member.description}
      </Text>
    </View>
  );

  const renderFeature = (feature) => (
    <View key={feature.title} style={[styles.featureCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
      <View style={[styles.featureIcon, { backgroundColor: theme.PRIMARY + '20' }]}>
        <Ionicons name={feature.icon} size={24} color={theme.PRIMARY} />
      </View>
      <View style={styles.featureContent}>
        <Text style={[styles.featureTitle, { color: theme.TEXT_PRIMARY }]}>
          {feature.title}
        </Text>
        <Text style={[styles.featureDescription, { color: theme.TEXT_SECONDARY }]}>
          {feature.description}
        </Text>
      </View>
    </View>
  );

  const renderSocialLink = (social) => (
    <TouchableOpacity
      key={social.platform}
      style={[styles.socialButton, { backgroundColor: social.color + '20' }]}
      onPress={() => handleSocialPress(social.url)}
    >
      <Ionicons name={social.icon} size={24} color={social.color} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>About BuildConnect</Text>
          <View style={{ width: 24 }} />
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* App Logo & Info */}
        <View style={styles.appInfoSection}>
          <View style={[styles.logoContainer, { backgroundColor: theme.PRIMARY }]}>
            <Ionicons name="business" size={48} color="#fff" />
          </View>
          <Text style={[styles.appName, { color: theme.TEXT_PRIMARY }]}>
            BuildConnect
          </Text>
          <Text style={[styles.appVersion, { color: theme.TEXT_SECONDARY }]}>
            Version 1.0.0
          </Text>
          <Text style={[styles.appDescription, { color: theme.TEXT_SECONDARY }]}>
            Connecting property seekers with trusted contractors and land brokers across India
          </Text>
        </View>

        {/* Mission Statement */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Our Mission
          </Text>
          <Text style={[styles.missionText, { color: theme.TEXT_SECONDARY }]}>
            To revolutionize the real estate and construction industry by providing a seamless platform 
            that connects property seekers, contractors, and land brokers. We aim to make property 
            transactions transparent, efficient, and trustworthy through innovative technology and 
            verified professionals.
          </Text>
        </View>

        {/* Key Features */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Key Features
          </Text>
          <View style={styles.featuresGrid}>
            {features.map(renderFeature)}
          </View>
        </View>

        {/* Team */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Meet Our Team
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.teamContainer}
          >
            {teamMembers.map(renderTeamMember)}
          </ScrollView>
        </View>

        {/* Company Stats */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Our Impact
          </Text>
          <View style={styles.statsContainer}>
            <View style={[styles.statCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
              <Text style={[styles.statNumber, { color: theme.PRIMARY }]}>10K+</Text>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Properties Listed</Text>
            </View>
            <View style={[styles.statCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
              <Text style={[styles.statNumber, { color: theme.PRIMARY }]}>5K+</Text>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Verified Contractors</Text>
            </View>
            <View style={[styles.statCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
              <Text style={[styles.statNumber, { color: theme.PRIMARY }]}>50K+</Text>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Happy Users</Text>
            </View>
          </View>
        </View>

        {/* Social Links */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Connect With Us
          </Text>
          <View style={styles.socialContainer}>
            {socialLinks.map(renderSocialLink)}
          </View>
        </View>

        {/* Legal Links */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Legal
          </Text>
          <TouchableOpacity
            style={[styles.legalLink, { backgroundColor: theme.CARD_BACKGROUND }]}
            onPress={() => router.push('/Legal/PrivacyPolicy')}
          >
            <Text style={[styles.legalText, { color: theme.TEXT_PRIMARY }]}>
              Privacy Policy
            </Text>
            <Ionicons name="chevron-forward" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.legalLink, { backgroundColor: theme.CARD_BACKGROUND }]}
            onPress={() => router.push('/Legal/TermsOfService')}
          >
            <Text style={[styles.legalText, { color: theme.TEXT_PRIMARY }]}>
              Terms of Service
            </Text>
            <Ionicons name="chevron-forward" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>

        {/* Contact Info */}
        <View style={[styles.section, styles.lastSection]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Contact Information
          </Text>
          <View style={[styles.contactCard, { backgroundColor: theme.CARD_BACKGROUND }]}>
            <View style={styles.contactRow}>
              <Ionicons name="mail" size={20} color={theme.PRIMARY} />
              <Text style={[styles.contactText, { color: theme.TEXT_SECONDARY }]}>
                <EMAIL>
              </Text>
            </View>
            <View style={styles.contactRow}>
              <Ionicons name="call" size={20} color={theme.PRIMARY} />
              <Text style={[styles.contactText, { color: theme.TEXT_SECONDARY }]}>
                +91 1800-123-4567
              </Text>
            </View>
            <View style={styles.contactRow}>
              <Ionicons name="location" size={20} color={theme.PRIMARY} />
              <Text style={[styles.contactText, { color: theme.TEXT_SECONDARY }]}>
                Bangalore, Karnataka, India
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  appInfoSection: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  appVersion: {
    fontSize: 16,
    marginBottom: 16,
  },
  appDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  lastSection: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  missionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  featuresGrid: {
    gap: 12,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  teamContainer: {
    paddingRight: 20,
  },
  teamCard: {
    width: 200,
    padding: 16,
    borderRadius: 12,
    marginRight: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  memberImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  memberName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  memberRole: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  memberDescription: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
  },
  socialButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 8,
  },
  legalLink: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  legalText: {
    fontSize: 16,
    fontWeight: '600',
  },
  contactCard: {
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactText: {
    fontSize: 16,
    marginLeft: 12,
  },
});

export default About;
