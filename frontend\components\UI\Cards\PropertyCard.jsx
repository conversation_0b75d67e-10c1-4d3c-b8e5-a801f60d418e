import React, { useContext } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../../context/ThemeContext';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2; // 2 cards per row with margins

const PropertyCard = ({
  property,
  onPress,
  onFavorite,
  onShare,
  style,
  variant = 'default', // 'default', 'featured', 'compact'
  showActions = true,
  showPrice = true,
  showLocation = true,
  showVerification = true,
}) => {
  const { theme } = useContext(ThemeContext);

  const formatPrice = (price) => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)}Cr`;
    } else if (price >= 100000) {
      return `₹${(price / 100000).toFixed(1)}L`;
    } else {
      return `₹${price.toLocaleString()}`;
    }
  };

  const getCardWidth = () => {
    switch (variant) {
      case 'featured':
        return width - 32;
      case 'compact':
        return CARD_WIDTH - 8;
      default:
        return CARD_WIDTH;
    }
  };

  const getImageHeight = () => {
    switch (variant) {
      case 'featured':
        return 200;
      case 'compact':
        return 120;
      default:
        return 160;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: theme.CARD_BACKGROUND,
          width: getCardWidth(),
        },
        style,
      ]}
      onPress={() => onPress?.(property)}
      activeOpacity={0.8}
    >
      {/* Image Container */}
      <View style={styles.imageContainer}>
        <Image
          source={{
            uri: property.images?.[0] || 'https://via.placeholder.com/300x200',
          }}
          style={[
            styles.image,
            {
              height: getImageHeight(),
            },
          ]}
          resizeMode="cover"
        />
        
        {/* Overlay Gradient */}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.3)']}
          style={styles.imageOverlay}
        />

        {/* Verification Badge */}
        {showVerification && property.isVerified && (
          <View style={[styles.verificationBadge, { backgroundColor: theme.SUCCESS }]}>
            <Ionicons name="checkmark-circle" size={12} color="#fff" />
            <Text style={styles.verificationText}>Verified</Text>
          </View>
        )}

        {/* Featured Badge */}
        {property.isFeatured && (
          <View style={[styles.featuredBadge, { backgroundColor: theme.WARNING }]}>
            <Ionicons name="star" size={12} color="#fff" />
            <Text style={styles.featuredText}>Featured</Text>
          </View>
        )}

        {/* Actions */}
        {showActions && (
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: 'rgba(255,255,255,0.9)' }]}
              onPress={() => onFavorite?.(property)}
            >
              <Ionicons
                name={property.isFavorited ? 'heart' : 'heart-outline'}
                size={16}
                color={property.isFavorited ? theme.ERROR : theme.TEXT_SECONDARY}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: 'rgba(255,255,255,0.9)' }]}
              onPress={() => onShare?.(property)}
            >
              <Ionicons name="share-outline" size={16} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          </View>
        )}

        {/* Price Tag */}
        {showPrice && (
          <View style={styles.priceContainer}>
            <Text style={styles.priceText}>{formatPrice(property.price)}</Text>
            {property.priceType && (
              <Text style={styles.priceTypeText}>/{property.priceType}</Text>
            )}
          </View>
        )}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Title */}
        <Text
          style={[styles.title, { color: theme.TEXT_PRIMARY }]}
          numberOfLines={variant === 'compact' ? 1 : 2}
        >
          {property.title}
        </Text>

        {/* Location */}
        {showLocation && (
          <View style={styles.locationContainer}>
            <Ionicons name="location-outline" size={12} color={theme.TEXT_SECONDARY} />
            <Text
              style={[styles.locationText, { color: theme.TEXT_SECONDARY }]}
              numberOfLines={1}
            >
              {property.location?.address || property.location?.city}
            </Text>
          </View>
        )}

        {/* Property Details */}
        <View style={styles.detailsContainer}>
          {property.area && (
            <View style={styles.detailItem}>
              <Ionicons name="resize-outline" size={12} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.detailText, { color: theme.TEXT_SECONDARY }]}>
                {property.area} {property.areaUnit || 'sq ft'}
              </Text>
            </View>
          )}
          
          {property.type && (
            <View style={styles.detailItem}>
              <Ionicons name="business-outline" size={12} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.detailText, { color: theme.TEXT_SECONDARY }]}>
                {property.type}
              </Text>
            </View>
          )}
        </View>

        {/* Additional Info for Featured Variant */}
        {variant === 'featured' && (
          <View style={styles.additionalInfo}>
            {property.description && (
              <Text
                style={[styles.description, { color: theme.TEXT_SECONDARY }]}
                numberOfLines={2}
              >
                {property.description}
              </Text>
            )}
            
            {property.amenities && property.amenities.length > 0 && (
              <View style={styles.amenitiesContainer}>
                {property.amenities.slice(0, 3).map((amenity, index) => (
                  <View
                    key={index}
                    style={[styles.amenityChip, { backgroundColor: theme.INPUT_BACKGROUND }]}
                  >
                    <Text style={[styles.amenityText, { color: theme.TEXT_PRIMARY }]}>
                      {amenity}
                    </Text>
                  </View>
                ))}
                {property.amenities.length > 3 && (
                  <Text style={[styles.moreAmenities, { color: theme.TEXT_SECONDARY }]}>
                    +{property.amenities.length - 3} more
                  </Text>
                )}
              </View>
            )}
          </View>
        )}

        {/* Footer */}
        <View style={styles.footer}>
          {property.broker && (
            <View style={styles.brokerInfo}>
              <Image
                source={{
                  uri: property.broker.avatar || 'https://via.placeholder.com/24x24',
                }}
                style={styles.brokerAvatar}
              />
              <Text style={[styles.brokerName, { color: theme.TEXT_SECONDARY }]}>
                {property.broker.name}
              </Text>
            </View>
          )}
          
          {property.postedAt && (
            <Text style={[styles.postedTime, { color: theme.TEXT_SECONDARY }]}>
              {new Date(property.postedAt).toLocaleDateString()}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  verificationBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  verificationText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#fff',
    marginLeft: 2,
  },
  featuredBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  featuredText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#fff',
    marginLeft: 2,
  },
  actionsContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'column',
    gap: 4,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  priceContainer: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  priceText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  priceTypeText: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.8,
  },
  content: {
    padding: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  locationText: {
    fontSize: 12,
    marginLeft: 4,
    flex: 1,
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 11,
    marginLeft: 2,
  },
  additionalInfo: {
    marginBottom: 8,
  },
  description: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 6,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
    alignItems: 'center',
  },
  amenityChip: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  amenityText: {
    fontSize: 10,
  },
  moreAmenities: {
    fontSize: 10,
    fontStyle: 'italic',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  brokerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  brokerAvatar: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 4,
  },
  brokerName: {
    fontSize: 10,
    flex: 1,
  },
  postedTime: {
    fontSize: 10,
  },
});

export default PropertyCard;
