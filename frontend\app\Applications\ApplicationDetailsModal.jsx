import React, { useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';

const ApplicationDetailsModal = ({ visible, onClose, data, onPreview }) => {
    const { theme } = useContext(ThemeContext);

    if (!data) return null;

    // Define field configurations based on type
    const getFields = () => {
        const commonFields = [
            {
                label: 'Type',
                value:
                    data.type === 'brokers' ? 'site scout' : data.type || 'N/A',
            },
            { label: 'Name', value: data.nameOnAadhaar || 'N/A' },
            { label: 'Aadhaar Number', value: data.aadhaarNumber || 'N/A' },
            { label: 'PAN Number', value: data.panNumber || 'N/A' },
            {
                label: 'Date of Birth',
                value: data.dateOfBirth
                    ? new Date(data.dateOfBirth).toLocaleDateString()
                    : 'N/A',
            },
            { label: 'Gender', value: data.gender || 'N/A' },
            { label: 'Address', value: data.address || 'N/A' },
            {
                label: 'Service Areas',
                value:
                    Array.isArray(data.serviceAreas) &&
                    data.serviceAreas.length > 0
                        ? data.serviceAreas.join(', ')
                        : 'N/A',
            },
        ];

        const contractorFields = [
            {
                label: 'Specialties',
                value:
                    Array.isArray(data.specialties) &&
                    data.specialties.length > 0
                        ? data.specialties.join(', ')
                        : 'N/A',
            },
            { label: 'Experience', value: data.experience || 'N/A' },
        ];

        const requestFields = [
            { label: 'Request ID', value: data.requestId || data.id || 'N/A' },
            {
                label: 'Request Type',
                value: data.requestType || data.type || 'N/A',
            },
            {
                label: 'Status',
                value: data.status
                    ? data.status.charAt(0).toUpperCase() + data.status.slice(1)
                    : 'N/A',
            },
            { label: 'Description', value: data.description || 'N/A' },
        ];

        if (data.type === 'requests') {
            return requestFields.filter(
                (field) => field.value !== 'N/A' && field.value !== ''
            );
        } else if (data.type === 'contractors') {
            return [...commonFields, ...contractorFields].filter(
                (field) => field.value !== 'N/A' && field.value !== ''
            );
        } else {
            // Site Scouts or other types
            return commonFields.filter(
                (field) => field.value !== 'N/A' && field.value !== ''
            );
        }
    };

    const fields = getFields();

    return (
        <Modal
            visible={visible}
            transparent
            animationType="slide"
            onRequestClose={onClose}
        >
            <View style={styles.modalOverlay}>
                <View
                    style={[
                        styles.modalContainer,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <View style={styles.modalHeader}>
                        <Text
                            style={[
                                styles.modalTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {data.type === 'requests'
                                ? 'Request Details'
                                : 'Application Details'}
                        </Text>
                        <TouchableOpacity onPress={onClose}>
                            <MaterialIcons
                                name="close"
                                size={24}
                                color={theme.TEXT_PRIMARY}
                            />
                        </TouchableOpacity>
                    </View>
                    {fields.length > 0 ? (
                        <View style={styles.tableContainer}>
                            {fields.map((field, index) => (
                                <View
                                    key={field.label}
                                    style={[
                                        styles.tableRow,
                                        index % 2 === 1
                                            ? {
                                                  backgroundColor:
                                                      theme.CARD + '22',
                                              }
                                            : {},
                                        index === fields.length - 1
                                            ? { borderBottomWidth: 0 }
                                            : {},
                                    ]}
                                >
                                    <View style={styles.tableLabel}>
                                        <Text
                                            style={[
                                                styles.modalDetail,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    fontWeight: 'bold',
                                                },
                                            ]}
                                        >
                                            {field.label}
                                        </Text>
                                    </View>
                                    <View style={styles.tableValue}>
                                        <Text
                                            style={[
                                                styles.modalDetail,
                                                { color: theme.TEXT_SECONDARY },
                                            ]}
                                        >
                                            {field.value}
                                        </Text>
                                    </View>
                                </View>
                            ))}
                        </View>
                    ) : (
                        <Text
                            style={[
                                styles.noDataText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            No details available.
                        </Text>
                    )}
                    {data.type !== 'requests' && (
                        <View style={styles.modalButtonContainer}>
                            <TouchableOpacity
                                onPress={() =>
                                    onPreview(data.aadhaarDocument, 'Aadhaar')
                                }
                                style={styles.modalButton}
                                disabled={!data.aadhaarDocument}
                            >
                                <LinearGradient
                                    colors={[
                                        data.aadhaarDocument
                                            ? theme.PRIMARY
                                            : theme.GRAY_LIGHT,
                                        data.aadhaarDocument
                                            ? theme.SECONDARY
                                            : theme.GRAY_LIGHT,
                                    ]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    style={styles.modalButtonGradient}
                                >
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            paddingHorizontal: 4,
                                            gap: 8,
                                        }}
                                    >
                                        <Ionicons
                                            name="document-text-outline"
                                            size={20}
                                            color={theme.WHITE}
                                            style={styles.icon}
                                        />
                                        <Text
                                            style={[
                                                styles.modalButtonText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            Aadhaar
                                        </Text>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>
                            <TouchableOpacity
                                onPress={() =>
                                    onPreview(data.panDocument, 'PAN')
                                }
                                style={styles.modalButton}
                                disabled={!data.panDocument}
                            >
                                <LinearGradient
                                    colors={[
                                        data.panDocument
                                            ? theme.PRIMARY
                                            : theme.GRAY_LIGHT,
                                        data.panDocument
                                            ? theme.SECONDARY
                                            : theme.GRAY_LIGHT,
                                    ]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    style={styles.modalButtonGradient}
                                >
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            paddingHorizontal: 4,
                                            gap: 8,
                                        }}
                                    >
                                        <Ionicons
                                            name="document-text-outline"
                                            size={20}
                                            color={theme.WHITE}
                                            style={styles.icon}
                                        />
                                        <Text
                                            style={[
                                                styles.modalButtonText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            PAN
                                        </Text>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    tableContainer: {
        marginBottom: 16,
        justifyContent: 'center',
        alignItems: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        paddingVertical: 8,
        alignItems: 'center',
    },
    tableLabel: {
        flex: 1,
        paddingRight: 8,
    },
    tableValue: {
        flex: 1,
    },
    modalDetail: {
        fontSize: 16,
    },
    modalButtonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    modalButton: {
        flex: 1,
        marginHorizontal: 8,
    },
    modalButtonGradient: {
        paddingVertical: 12,
        borderRadius: 12,
        alignItems: 'center',
    },
    modalButtonText: {
        fontSize: 14,
        fontWeight: '700',
    },
    noDataText: {
        fontSize: 16,
        textAlign: 'center',
        marginVertical: 16,
    },
});

export default ApplicationDetailsModal;
