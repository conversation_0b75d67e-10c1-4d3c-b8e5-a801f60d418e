import axios from 'axios';
import { API_BASE_URL } from '../index';

const paymentApi = axios.create({
  baseURL: `${API_BASE_URL}/api/payment`,
});

// Add auth token to requests
paymentApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Razorpay Integration
export const createRazorpayOrder = async (orderData) => {
  const { data } = await paymentApi.post('/razorpay/create-order', orderData);
  return data;
};

export const verifyRazorpayPayment = async (paymentData) => {
  const { data } = await paymentApi.post('/razorpay/verify-payment', paymentData);
  return data;
};

export const captureRazorpayPayment = async (paymentId, amount) => {
  const { data } = await paymentApi.post('/razorpay/capture-payment', {
    paymentId,
    amount,
  });
  return data;
};

// Stripe Integration
export const createStripePaymentIntent = async (paymentData) => {
  const { data } = await paymentApi.post('/stripe/create-payment-intent', paymentData);
  return data;
};

export const confirmStripePayment = async (paymentIntentId, paymentMethodId) => {
  const { data } = await paymentApi.post('/stripe/confirm-payment', {
    paymentIntentId,
    paymentMethodId,
  });
  return data;
};

export const createStripeCustomer = async (customerData) => {
  const { data } = await paymentApi.post('/stripe/create-customer', customerData);
  return data;
};

export const getStripeCustomer = async (customerId) => {
  const { data } = await paymentApi.get(`/stripe/customer/${customerId}`);
  return data;
};

// Commission Payments
export const createCommissionPayment = async (commissionData) => {
  const { data } = await paymentApi.post('/commission/create', commissionData);
  return data;
};

export const processCommissionPayment = async (commissionId, paymentMethod = 'razorpay') => {
  const { data } = await paymentApi.post(`/commission/${commissionId}/process`, {
    paymentMethod,
  });
  return data;
};

export const getBrokerCommissions = async (brokerId, status = 'all') => {
  const { data } = await paymentApi.get(`/commission/broker/${brokerId}`, {
    params: { status }, // 'pending', 'paid', 'failed', 'all'
  });
  return data;
};

export const getContractorPayments = async (contractorId, status = 'all') => {
  const { data } = await paymentApi.get(`/commission/contractor/${contractorId}`, {
    params: { status },
  });
  return data;
};

// Transaction Management
export const createTransaction = async (transactionData) => {
  const { data } = await paymentApi.post('/transactions', transactionData);
  return data;
};

export const getTransaction = async (transactionId) => {
  const { data } = await paymentApi.get(`/transactions/${transactionId}`);
  return data;
};

export const getUserTransactions = async (userId, page = 1, limit = 20) => {
  const { data } = await paymentApi.get(`/transactions/user/${userId}`, {
    params: { page, limit },
  });
  return data;
};

export const updateTransactionStatus = async (transactionId, status, metadata = {}) => {
  const { data } = await paymentApi.put(`/transactions/${transactionId}/status`, {
    status, // 'pending', 'processing', 'completed', 'failed', 'cancelled'
    metadata,
  });
  return data;
};

// Land Purchase Payments
export const createLandPurchasePayment = async (landId, buyerId, amount, paymentMethod = 'razorpay') => {
  const { data } = await paymentApi.post('/land-purchase/create', {
    landId,
    buyerId,
    amount,
    paymentMethod,
  });
  return data;
};

export const processLandPurchasePayment = async (purchaseId, paymentData) => {
  const { data } = await paymentApi.post(`/land-purchase/${purchaseId}/process`, paymentData);
  return data;
};

export const getLandPurchaseHistory = async (userId) => {
  const { data } = await paymentApi.get(`/land-purchase/user/${userId}`);
  return data;
};

// Contractor Service Payments
export const createServicePayment = async (serviceData) => {
  const { data } = await paymentApi.post('/service-payment/create', serviceData);
  return data;
};

export const processServicePayment = async (serviceId, paymentData) => {
  const { data } = await paymentApi.post(`/service-payment/${serviceId}/process`, paymentData);
  return data;
};

export const getServicePayments = async (contractorId) => {
  const { data } = await paymentApi.get(`/service-payment/contractor/${contractorId}`);
  return data;
};

// Escrow Services
export const createEscrowPayment = async (escrowData) => {
  const { data } = await paymentApi.post('/escrow/create', escrowData);
  return data;
};

export const releaseEscrowPayment = async (escrowId, releaseData) => {
  const { data } = await paymentApi.post(`/escrow/${escrowId}/release`, releaseData);
  return data;
};

export const disputeEscrowPayment = async (escrowId, disputeReason) => {
  const { data } = await paymentApi.post(`/escrow/${escrowId}/dispute`, {
    reason: disputeReason,
  });
  return data;
};

export const getEscrowPayments = async (userId) => {
  const { data } = await paymentApi.get(`/escrow/user/${userId}`);
  return data;
};

// Payment Methods Management
export const addPaymentMethod = async (paymentMethodData) => {
  const { data } = await paymentApi.post('/payment-methods', paymentMethodData);
  return data;
};

export const getUserPaymentMethods = async (userId) => {
  const { data } = await paymentApi.get(`/payment-methods/user/${userId}`);
  return data;
};

export const updatePaymentMethod = async (methodId, updateData) => {
  const { data } = await paymentApi.put(`/payment-methods/${methodId}`, updateData);
  return data;
};

export const deletePaymentMethod = async (methodId) => {
  const { data } = await paymentApi.delete(`/payment-methods/${methodId}`);
  return data;
};

export const setDefaultPaymentMethod = async (methodId) => {
  const { data } = await paymentApi.post(`/payment-methods/${methodId}/set-default`);
  return data;
};

// Refunds
export const createRefund = async (refundData) => {
  const { data } = await paymentApi.post('/refunds', refundData);
  return data;
};

export const processRefund = async (refundId) => {
  const { data } = await paymentApi.post(`/refunds/${refundId}/process`);
  return data;
};

export const getRefunds = async (userId) => {
  const { data } = await paymentApi.get(`/refunds/user/${userId}`);
  return data;
};

// Payment Analytics
export const getPaymentAnalytics = async (userId, timeframe = '30d') => {
  const { data } = await paymentApi.get(`/analytics/user/${userId}`, {
    params: { timeframe }, // '7d', '30d', '90d', '1y'
  });
  return data;
};

export const getBrokerEarnings = async (brokerId, timeframe = '30d') => {
  const { data } = await paymentApi.get(`/analytics/broker/${brokerId}/earnings`, {
    params: { timeframe },
  });
  return data;
};

export const getContractorEarnings = async (contractorId, timeframe = '30d') => {
  const { data } = await paymentApi.get(`/analytics/contractor/${contractorId}/earnings`, {
    params: { timeframe },
  });
  return data;
};

// Subscription Management (for premium features)
export const createSubscription = async (subscriptionData) => {
  const { data } = await paymentApi.post('/subscriptions', subscriptionData);
  return data;
};

export const cancelSubscription = async (subscriptionId) => {
  const { data } = await paymentApi.post(`/subscriptions/${subscriptionId}/cancel`);
  return data;
};

export const getUserSubscription = async (userId) => {
  const { data } = await paymentApi.get(`/subscriptions/user/${userId}`);
  return data;
};

export const updateSubscription = async (subscriptionId, updateData) => {
  const { data } = await paymentApi.put(`/subscriptions/${subscriptionId}`, updateData);
  return data;
};

// Wallet Management
export const getWalletBalance = async (userId) => {
  const { data } = await paymentApi.get(`/wallet/${userId}/balance`);
  return data;
};

export const addMoneyToWallet = async (userId, amount, paymentMethod = 'razorpay') => {
  const { data } = await paymentApi.post(`/wallet/${userId}/add-money`, {
    amount,
    paymentMethod,
  });
  return data;
};

export const withdrawFromWallet = async (userId, amount, bankDetails) => {
  const { data } = await paymentApi.post(`/wallet/${userId}/withdraw`, {
    amount,
    bankDetails,
  });
  return data;
};

export const getWalletTransactions = async (userId, page = 1, limit = 20) => {
  const { data } = await paymentApi.get(`/wallet/${userId}/transactions`, {
    params: { page, limit },
  });
  return data;
};

// Bank Account Management
export const addBankAccount = async (userId, bankDetails) => {
  const { data } = await paymentApi.post(`/bank-accounts/${userId}`, bankDetails);
  return data;
};

export const getUserBankAccounts = async (userId) => {
  const { data } = await paymentApi.get(`/bank-accounts/${userId}`);
  return data;
};

export const verifyBankAccount = async (accountId, verificationData) => {
  const { data } = await paymentApi.post(`/bank-accounts/${accountId}/verify`, verificationData);
  return data;
};

export const deleteBankAccount = async (accountId) => {
  const { data } = await paymentApi.delete(`/bank-accounts/${accountId}`);
  return data;
};

export default paymentApi;
