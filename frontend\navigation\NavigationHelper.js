import { CommonActions } from '@react-navigation/native';

// Navigation helper functions for the BuildConnect app
export class NavigationHelper {
  static navigationRef = null;

  static setNavigationRef(ref) {
    this.navigationRef = ref;
  }

  // Core navigation functions
  static navigate(routeName, params = {}) {
    if (this.navigationRef?.isReady()) {
      this.navigationRef.navigate(routeName, params);
    }
  }

  static goBack() {
    if (this.navigationRef?.isReady() && this.navigationRef.canGoBack()) {
      this.navigationRef.goBack();
    }
  }

  static reset(routeName, params = {}) {
    if (this.navigationRef?.isReady()) {
      this.navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: routeName, params }],
        })
      );
    }
  }

  // Property-related navigation
  static navigateToLandList(filters = {}) {
    this.navigate('LandList', filters);
  }

  static navigateToLandDetails(landId) {
    this.navigate('LandDetails', { landId });
  }

  static navigateToSellLand() {
    this.navigate('SellLand');
  }

  static navigateToPropertySearch(searchParams = {}) {
    this.navigate('PropertySearch', searchParams);
  }

  // Contractor-related navigation
  static navigateToContractorList(filters = {}) {
    this.navigate('ContractorList', filters);
  }

  static navigateToContractorProfile(contractorId) {
    this.navigate('ContractorProfile', { contractorId });
  }

  static navigateToContractorServices(contractorId) {
    this.navigate('ContractorServices', { contractorId });
  }

  static navigateToHireContractor(contractorId, serviceId = null) {
    this.navigate('HireContractor', { contractorId, serviceId });
  }

  // Chat-related navigation
  static navigateToChatList() {
    this.navigate('ChatList');
  }

  static navigateToChatRoom(chatRoomId, chatType = 'direct') {
    this.navigate('ChatRoom', { chatRoomId, chatType });
  }

  static navigateToCreateChat(participants = []) {
    this.navigate('CreateChat', { participants });
  }

  // Payment-related navigation
  static navigateToPayment(paymentData) {
    this.navigate('PaymentScreen', paymentData);
  }

  static navigateToWallet() {
    this.navigate('WalletScreen');
  }

  static navigateToTransactionHistory() {
    this.navigate('TransactionHistory');
  }

  static navigateToPaymentSuccess(transactionId) {
    this.navigate('PaymentSuccess', { transactionId });
  }

  // Map-related navigation
  static navigateToMapExplorer(initialLocation = null) {
    this.navigate('MapExplorer', { initialLocation });
  }

  static navigateToLocationPicker(callback) {
    this.navigate('LocationPicker', { callback });
  }

  // AI-related navigation
  static navigateToAIAssistant() {
    this.navigate('AIAssistant');
  }

  static navigateToAIRecommendations(type = 'properties') {
    this.navigate('AIRecommendations', { type });
  }

  static navigateToMarketAnalysis(location = null) {
    this.navigate('MarketAnalysis', { location });
  }

  static navigateToDocumentValidation(documents = []) {
    this.navigate('DocumentValidation', { documents });
  }

  // Admin-related navigation
  static navigateToAdminDashboard() {
    this.navigate('AdminDashboard');
  }

  static navigateToUserManagement() {
    this.navigate('UserManagement');
  }

  static navigateToContentModeration() {
    this.navigate('ContentModeration');
  }

  static navigateToSystemSettings() {
    this.navigate('SystemSettings');
  }

  // Profile and settings navigation
  static navigateToProfile(userId = null) {
    this.navigate('Profile', userId ? { userId } : {});
  }

  static navigateToSettings() {
    this.navigate('Settings');
  }

  static navigateToEditProfile() {
    this.navigate('EditProfile');
  }

  static navigateToNotifications() {
    this.navigate('NotificationsList');
  }

  // Authentication navigation
  static navigateToLogin() {
    this.reset('Login');
  }

  static navigateToRegister() {
    this.navigate('Register');
  }

  static navigateToForgotPassword() {
    this.navigate('ForgotPassword');
  }

  static navigateToHome() {
    this.reset('Home');
  }

  // Broker-specific navigation
  static navigateToBrokerDashboard() {
    this.navigate('BrokerDashboard');
  }

  static navigateToBrokerProfile(brokerId) {
    this.navigate('BrokerProfile', { brokerId });
  }

  static navigateToBrokerListings(brokerId) {
    this.navigate('BrokerListings', { brokerId });
  }

  // Document management navigation
  static navigateToDocumentManager() {
    this.navigate('DocumentManager');
  }

  static navigateToDocumentViewer(documentId) {
    this.navigate('DocumentViewer', { documentId });
  }

  static navigateToDocumentUpload(category = 'general') {
    this.navigate('DocumentUpload', { category });
  }

  // Support and help navigation
  static navigateToHelpSupport() {
    this.navigate('HelpSupport');
  }

  static navigateToCreateTicket() {
    this.navigate('CreateTicket');
  }

  static navigateToTicketDetails(ticketId) {
    this.navigate('TicketDetails', { ticketId });
  }

  static navigateToFAQ() {
    this.navigate('FAQ');
  }

  // Utility functions
  static getCurrentRoute() {
    if (this.navigationRef?.isReady()) {
      return this.navigationRef.getCurrentRoute();
    }
    return null;
  }

  static getState() {
    if (this.navigationRef?.isReady()) {
      return this.navigationRef.getState();
    }
    return null;
  }

  // Deep linking helpers
  static handleDeepLink(url) {
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname;
      const params = Object.fromEntries(urlObj.searchParams);

      switch (path) {
        case '/land':
          if (params.id) {
            this.navigateToLandDetails(params.id);
          } else {
            this.navigateToLandList(params);
          }
          break;
        case '/contractor':
          if (params.id) {
            this.navigateToContractorProfile(params.id);
          } else {
            this.navigateToContractorList(params);
          }
          break;
        case '/chat':
          if (params.roomId) {
            this.navigateToChatRoom(params.roomId);
          } else {
            this.navigateToChatList();
          }
          break;
        case '/payment':
          this.navigateToPayment(params);
          break;
        case '/map':
          this.navigateToMapExplorer(params.location ? JSON.parse(params.location) : null);
          break;
        default:
          this.navigateToHome();
      }
    } catch (error) {
      console.error('Error handling deep link:', error);
      this.navigateToHome();
    }
  }

  // Navigation state helpers
  static isCurrentRoute(routeName) {
    const currentRoute = this.getCurrentRoute();
    return currentRoute?.name === routeName;
  }

  static canGoBack() {
    return this.navigationRef?.isReady() && this.navigationRef.canGoBack();
  }

  // Tab navigation helpers
  static switchToTab(tabName) {
    if (this.navigationRef?.isReady()) {
      this.navigationRef.navigate(tabName);
    }
  }

  static switchToHomeTab() {
    this.switchToTab('Home');
  }

  static switchToListingsTab() {
    this.switchToTab('Listings');
  }

  static switchToChatsTab() {
    this.switchToTab('Chats');
  }

  static switchToMoreTab() {
    this.switchToTab('More');
  }

  // Modal navigation helpers
  static openModal(modalName, params = {}) {
    this.navigate(modalName, { ...params, isModal: true });
  }

  static closeModal() {
    this.goBack();
  }

  // Stack navigation helpers
  static pushToStack(routeName, params = {}) {
    if (this.navigationRef?.isReady()) {
      this.navigationRef.dispatch(
        CommonActions.navigate({
          name: routeName,
          params,
        })
      );
    }
  }

  static popFromStack(count = 1) {
    if (this.navigationRef?.isReady()) {
      this.navigationRef.dispatch(
        CommonActions.goBack()
      );
    }
  }

  static popToTop() {
    if (this.navigationRef?.isReady()) {
      this.navigationRef.dispatch(
        CommonActions.popToTop()
      );
    }
  }
}

export default NavigationHelper;
