import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Image,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

const ContractorList = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Mock data for contractors
  const contractors = [
    {
      id: 1,
      name: 'John Construction Co.',
      category: 'Building',
      rating: 4.8,
      reviews: 156,
      location: 'Mumbai, Maharashtra',
      experience: '8 years',
      price: '₹50,000 - ₹2,00,000',
      image: 'https://via.placeholder.com/100',
      verified: true,
      specialties: ['Residential', 'Commercial', 'Renovation'],
    },
    {
      id: 2,
      name: 'Elite Builders',
      category: 'Architecture',
      rating: 4.9,
      reviews: 203,
      location: 'Delhi, NCR',
      experience: '12 years',
      price: '₹75,000 - ₹5,00,000',
      image: 'https://via.placeholder.com/100',
      verified: true,
      specialties: ['Luxury Homes', 'Interior Design', 'Planning'],
    },
    {
      id: 3,
      name: 'Quick Fix Services',
      category: 'Maintenance',
      rating: 4.6,
      reviews: 89,
      location: 'Bangalore, Karnataka',
      experience: '5 years',
      price: '₹5,000 - ₹50,000',
      image: 'https://via.placeholder.com/100',
      verified: false,
      specialties: ['Plumbing', 'Electrical', 'Painting'],
    },
  ];

  const categories = [
    { id: 'all', name: 'All', icon: 'grid' },
    { id: 'Building', name: 'Building', icon: 'business' },
    { id: 'Architecture', name: 'Architecture', icon: 'library' },
    { id: 'Maintenance', name: 'Maintenance', icon: 'construct' },
    { id: 'Interior', name: 'Interior', icon: 'color-palette' },
  ];

  const filteredContractors = contractors.filter(contractor => {
    const matchesSearch = contractor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         contractor.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || contractor.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const renderContractor = (contractor) => (
    <TouchableOpacity
      key={contractor.id}
      style={[styles.contractorCard, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={() => router.push(`/Contractors/ContractorDetails?id=${contractor.id}`)}
    >
      <View style={styles.contractorHeader}>
        <Image source={{ uri: contractor.image }} style={styles.contractorImage} />
        <View style={styles.contractorInfo}>
          <View style={styles.nameRow}>
            <Text style={[styles.contractorName, { color: theme.TEXT_PRIMARY }]}>
              {contractor.name}
            </Text>
            {contractor.verified && (
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            )}
          </View>
          <Text style={[styles.contractorCategory, { color: theme.TEXT_SECONDARY }]}>
            {contractor.category} • {contractor.experience}
          </Text>
          <View style={styles.ratingRow}>
            <Ionicons name="star" size={16} color="#FFD700" />
            <Text style={[styles.rating, { color: theme.TEXT_PRIMARY }]}>
              {contractor.rating}
            </Text>
            <Text style={[styles.reviews, { color: theme.TEXT_SECONDARY }]}>
              ({contractor.reviews} reviews)
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.contractorDetails}>
        <View style={styles.locationRow}>
          <Ionicons name="location" size={16} color={theme.TEXT_SECONDARY} />
          <Text style={[styles.location, { color: theme.TEXT_SECONDARY }]}>
            {contractor.location}
          </Text>
        </View>
        <Text style={[styles.price, { color: theme.PRIMARY }]}>
          {contractor.price}
        </Text>
      </View>

      <View style={styles.specialtiesContainer}>
        {contractor.specialties.map((specialty, index) => (
          <View key={index} style={[styles.specialtyTag, { backgroundColor: theme.PRIMARY + '20' }]}>
            <Text style={[styles.specialtyText, { color: theme.PRIMARY }]}>
              {specialty}
            </Text>
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Find Contractors</Text>
          <TouchableOpacity onPress={() => router.push('/Contractors/ContractorFilters')}>
            <Ionicons name="filter" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: theme.CARD_BACKGROUND }]}>
        <Ionicons name="search" size={20} color={theme.TEXT_SECONDARY} />
        <TextInput
          style={[styles.searchInput, { color: theme.TEXT_PRIMARY }]}
          placeholder="Search contractors..."
          placeholderTextColor={theme.TEXT_SECONDARY}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Categories */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              {
                backgroundColor: selectedCategory === category.id ? theme.PRIMARY : theme.CARD_BACKGROUND,
              }
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Ionicons
              name={category.icon}
              size={20}
              color={selectedCategory === category.id ? '#fff' : theme.TEXT_SECONDARY}
            />
            <Text
              style={[
                styles.categoryText,
                {
                  color: selectedCategory === category.id ? '#fff' : theme.TEXT_SECONDARY,
                }
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Contractors List */}
      <ScrollView
        style={styles.contractorsList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contractorsContent}
      >
        {filteredContractors.map(renderContractor)}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 20,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 16,
  },
  categoriesContainer: {
    marginBottom: 10,
  },
  categoriesContent: {
    paddingHorizontal: 20,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  categoryText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
  },
  contractorsList: {
    flex: 1,
  },
  contractorsContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  contractorCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  contractorHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  contractorImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  contractorInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  contractorName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  contractorCategory: {
    fontSize: 14,
    marginBottom: 4,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  reviews: {
    fontSize: 12,
    marginLeft: 4,
  },
  contractorDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  location: {
    fontSize: 14,
    marginLeft: 4,
  },
  price: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  specialtyTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  specialtyText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default ContractorList;
