import React, { useContext } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../../context/ThemeContext';
import BackButton from './BackButton';
import { createStandardStyles, GRADIENT_PATTERNS } from './StandardStyles';

/**
 * StandardPageTemplate - A reusable template component that follows BuildConnect design patterns
 * 
 * This component provides the standard layout structure used across:
 * - Login page
 * - Profile page  
 * - BrokerForm page
 * - And other pages requiring consistent styling
 * 
 * @param {Object} props
 * @param {string} props.title - Main page title
 * @param {string} props.subtitle - Page subtitle (optional)
 * @param {React.ReactNode} props.children - Page content
 * @param {boolean} props.showBackButton - Whether to show back button (default: true)
 * @param {string} props.backButtonColor - Back button color (default: theme.WHITE)
 * @param {boolean} props.scrollable - Whether content should be scrollable (default: true)
 * @param {boolean} props.keyboardAvoiding - Whether to use KeyboardAvoidingView (default: true)
 * @param {Object} props.cardStyle - Additional styles for the card container
 * @param {Object} props.contentStyle - Additional styles for the content container
 * @param {Array} props.gradientColors - Custom gradient colors (optional)
 * @param {boolean} props.showLogo - Whether to show the BuildConnect logo (default: false)
 */
const StandardPageTemplate = ({
  title,
  subtitle,
  children,
  showBackButton = true,
  backButtonColor,
  scrollable = true,
  keyboardAvoiding = true,
  cardStyle = {},
  contentStyle = {},
  gradientColors,
  showLogo = false,
}) => {
  const { theme, isDarkMode } = useContext(ThemeContext);
  const styles = createStandardStyles(theme);
  
  // Determine gradient colors
  const overlayColors = gradientColors || GRADIENT_PATTERNS.PRIMARY;
  
  // Determine back button color
  const buttonColor = backButtonColor || theme.WHITE;

  const renderContent = () => (
    <>
      {showBackButton && <BackButton color={buttonColor} />}
      
      {/* Background Image with Gradient Overlay */}
      <View style={styles.backgroundContainer}>
        <Image
          source={require('../../../assets/images/background.png')}
          style={styles.backgroundImage}
          resizeMode="cover"
        />
        <LinearGradient
          colors={overlayColors}
          style={styles.backgroundOverlay}
          start={GRADIENT_PATTERNS.GRADIENT_PROPS.start}
          end={GRADIENT_PATTERNS.GRADIENT_PROPS.end}
        />
      </View>

      {/* Content Container */}
      <View style={[styles.contentContainer, contentStyle]}>
        {/* Logo (if enabled) */}
        {showLogo && (
          <View style={styles.logoContainer}>
            <Image
              source={require('../../../assets/images/build-connect.jpg')}
              style={[
                styles.logo,
                { borderColor: theme.LOGO_BORDER },
              ]}
              resizeMode="contain"
            />
          </View>
        )}

        {/* Main Card Container */}
        <View style={[styles.card, cardStyle]}>
          {/* Title and Subtitle */}
          {title && (
            <Text style={styles.title}>
              {title}
            </Text>
          )}
          
          {subtitle && (
            <Text style={styles.subtitle}>
              {subtitle}
            </Text>
          )}

          {/* Page Content */}
          {children}
        </View>
      </View>
    </>
  );

  const containerComponent = keyboardAvoiding ? (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      {renderContent()}
    </KeyboardAvoidingView>
  ) : (
    <View style={styles.container}>
      {renderContent()}
    </View>
  );

  if (scrollable) {
    return (
      <View style={styles.container}>
        <StatusBar
          barStyle={isDarkMode ? 'light-content' : 'dark-content'}
          backgroundColor={theme.PRIMARY}
        />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {keyboardAvoiding ? (
            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              style={{ flex: 1 }}
            >
              {renderContent()}
            </KeyboardAvoidingView>
          ) : (
            renderContent()
          )}
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.PRIMARY}
      />
      {containerComponent}
    </View>
  );
};

// Additional styles specific to this template
const templateStyles = {
  logoContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  
  logo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
  },
};

export default StandardPageTemplate;
