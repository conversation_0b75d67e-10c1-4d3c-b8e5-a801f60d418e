import React, { useContext } from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../../context/ThemeContext';
import { BUTTON_PATTERNS, SHADOW_PATTERNS } from './StandardStyles';

/**
 * StandardButton - A reusable button component following BuildConnect design patterns
 * 
 * This component provides consistent button styling across the app based on patterns from:
 * - Login page
 * - BrokerForm page
 * - Profile pages
 * 
 * @param {Object} props
 * @param {string} props.title - Button text
 * @param {function} props.onPress - Press handler
 * @param {string} props.variant - Button variant: 'primary', 'secondary', 'outline', 'text', 'gradient'
 * @param {string} props.size - Button size: 'small', 'medium', 'large'
 * @param {boolean} props.disabled - Whether button is disabled
 * @param {boolean} props.loading - Whether to show loading indicator
 * @param {string} props.iconName - Ionicons icon name (optional)
 * @param {string} props.iconPosition - Icon position: 'left' or 'right'
 * @param {Object} props.style - Additional button styles
 * @param {Object} props.textStyle - Additional text styles
 * @param {Array} props.gradientColors - Custom gradient colors for gradient variant
 * @param {boolean} props.fullWidth - Whether button should take full width
 */
const StandardButton = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  iconName,
  iconPosition = 'left',
  style = {},
  textStyle = {},
  gradientColors,
  fullWidth = false,
  ...props
}) => {
  const { theme } = useContext(ThemeContext);

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: 8,
          paddingHorizontal: 16,
          fontSize: 14,
        };
      case 'large':
        return {
          paddingVertical: 20,
          paddingHorizontal: 32,
          fontSize: 18,
        };
      default: // medium
        return {
          paddingVertical: 16,
          paddingHorizontal: 24,
          fontSize: 16,
        };
    }
  };

  const getVariantStyles = () => {
    const sizeStyles = getSizeStyles();
    
    switch (variant) {
      case 'secondary':
        return {
          button: {
            ...BUTTON_PATTERNS.SECONDARY,
            paddingVertical: sizeStyles.paddingVertical,
            paddingHorizontal: sizeStyles.paddingHorizontal,
            backgroundColor: theme.CARD,
            borderColor: theme.PRIMARY,
          },
          text: {
            ...BUTTON_PATTERNS.TEXT,
            fontSize: sizeStyles.fontSize,
            color: theme.PRIMARY,
          },
        };
      
      case 'outline':
        return {
          button: {
            ...BUTTON_PATTERNS.SECONDARY,
            paddingVertical: sizeStyles.paddingVertical,
            paddingHorizontal: sizeStyles.paddingHorizontal,
            backgroundColor: 'transparent',
            borderColor: theme.PRIMARY,
          },
          text: {
            ...BUTTON_PATTERNS.TEXT,
            fontSize: sizeStyles.fontSize,
            color: theme.PRIMARY,
          },
        };
      
      case 'text':
        return {
          button: {
            paddingVertical: sizeStyles.paddingVertical,
            paddingHorizontal: sizeStyles.paddingHorizontal,
            backgroundColor: 'transparent',
          },
          text: {
            ...BUTTON_PATTERNS.TEXT,
            fontSize: sizeStyles.fontSize,
            color: theme.PRIMARY,
          },
        };
      
      case 'gradient':
        return {
          button: {
            ...BUTTON_PATTERNS.PRIMARY,
            paddingVertical: sizeStyles.paddingVertical,
            paddingHorizontal: sizeStyles.paddingHorizontal,
            backgroundColor: 'transparent',
          },
          text: {
            ...BUTTON_PATTERNS.TEXT,
            fontSize: sizeStyles.fontSize,
            color: theme.WHITE,
          },
        };
      
      default: // primary
        return {
          button: {
            ...BUTTON_PATTERNS.PRIMARY,
            paddingVertical: sizeStyles.paddingVertical,
            paddingHorizontal: sizeStyles.paddingHorizontal,
            backgroundColor: theme.PRIMARY,
          },
          text: {
            ...BUTTON_PATTERNS.TEXT,
            fontSize: sizeStyles.fontSize,
            color: theme.WHITE,
          },
        };
    }
  };

  const getButtonStyle = () => {
    const variantStyles = getVariantStyles();
    const baseStyle = {
      ...variantStyles.button,
      opacity: disabled ? 0.6 : 1,
      width: fullWidth ? '100%' : 'auto',
      ...SHADOW_PATTERNS.LIGHT,
    };

    return [baseStyle, style];
  };

  const getTextStyle = () => {
    const variantStyles = getVariantStyles();
    return [variantStyles.text, textStyle];
  };

  const renderIcon = () => {
    if (!iconName || loading) return null;
    
    return (
      <Ionicons
        name={iconName}
        size={getSizeStyles().fontSize + 2}
        color={getVariantStyles().text.color}
        style={iconPosition === 'left' ? styles.iconLeft : styles.iconRight}
      />
    );
  };

  const renderContent = () => (
    <View style={styles.contentContainer}>
      {iconPosition === 'left' && renderIcon()}
      
      {loading ? (
        <ActivityIndicator
          size="small"
          color={getVariantStyles().text.color}
          style={styles.loader}
        />
      ) : (
        <Text style={getTextStyle()}>
          {title}
        </Text>
      )}
      
      {iconPosition === 'right' && renderIcon()}
    </View>
  );

  if (variant === 'gradient') {
    const colors = gradientColors || [theme.PRIMARY, theme.SECONDARY || theme.PRIMARY];
    
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled || loading}
        activeOpacity={0.8}
        {...props}
      >
        <LinearGradient
          colors={colors}
          style={getButtonStyle()}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  iconLeft: {
    marginRight: 8,
  },
  
  iconRight: {
    marginLeft: 8,
  },
  
  loader: {
    marginHorizontal: 8,
  },
});

export default StandardButton;
