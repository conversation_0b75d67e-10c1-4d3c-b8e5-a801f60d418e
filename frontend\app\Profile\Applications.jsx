import React, { useEffect, useState, useContext, useRef } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    StatusBar,
    Image,
    FlatList,
    Alert,
    RefreshControl,
    ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { fetchUserProfile } from '../../api/user/userApi';
import { fetchBrokerProfile } from '../../api/broker/brokerApi';
import BackButton from '../Components/Shared/BackButton';
import DocumentPreviewModal from '../Components/Profile/DocumentPreviewModal';
import ApplicationDetailsModal from '../Components/Profile/ApplicationDetailsModal';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const { height } = Dimensions.get('window');

export default function Applications() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const queryClient = useQueryClient();
    const [applications, setApplications] = useState({
        brokers: [],
        requests: [],
    });
    const [isFetching, setIsFetching] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [detailsModalVisible, setDetailsModalVisible] = useState(false);
    const [modalDocument, setModalDocument] = useState({ url: '', type: '' });
    const [selectedApplication, setSelectedApplication] = useState(null);
    const [section, setSection] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const flatListRef = useRef(null);

    useEffect(() => {
        if (flatListRef.current) {
            flatListRef.current.scrollToOffset({ offset: 0, animated: false });
        }
    }, [section]);

    const {
        data: user,
        isLoading: isUserLoading,
        error: userError,
    } = useQuery({
        queryKey: ['userProfile'],
        queryFn: fetchUserProfile,
        refetchInterval: 30000, // Poll every 30 seconds
        refetchIntervalInBackground: false, // Disable polling in background
        onError: () => {
            showToast('error', 'Error', 'Failed to fetch user profile.');
        },
    });

    const {
        data: brokerData,
        isLoading: isBrokerLoading,
        error: brokerError,
    } = useQuery({
        queryKey: ['brokerApplication'],
        queryFn: fetchBrokerProfile,
        refetchInterval: 30000, // Poll every 30 seconds
        refetchIntervalInBackground: false, // Disable polling in background
        onError: () => {
            showToast('error', 'Error', 'Failed to fetch broker data.');
        },
    });

    useEffect(() => {
        if (!isUserLoading && !isBrokerLoading && user) {
            const role = user.role.toLowerCase();
            const filteredApplications = { brokers: [], requests: [] };

            // Handle single object or array
            const data = brokerData
                ? Array.isArray(brokerData)
                    ? brokerData
                    : [brokerData]
                : [];

            if (role === 'user') {
                filteredApplications.brokers = data.map((app) => ({
                    ...app,
                    id: app.brokerId || app.id,
                    aadhaarDocument: app.aadhaarAsset,
                    panDocument: app.panAsset,
                    status: app.status || 'pending',
                }));
                setSection(data.length > 0 ? 'brokers' : null);
            } else if (role === 'broker' || role === 'contractor') {
                filteredApplications.requests = data.map((req) => ({
                    ...req,
                    id: req.requestId || req.id,
                    status: req.status || 'pending',
                }));
                setSection(data.length > 0 ? 'requests' : null);
            }

            setApplications(filteredApplications);
            setIsFetching(false);
        }
    }, [isUserLoading, isBrokerLoading, user, brokerData]);

    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await queryClient.invalidateQueries({ queryKey: ['userProfile'] });
            await queryClient.invalidateQueries({
                queryKey: ['brokerApplication'],
            });
        } catch (error) {
            showToast('error', 'Error', 'Failed to refresh data.');
        } finally {
            setRefreshing(false);
        }
    };

    const handlePreview = (url, documentType) => {
        if (url && url.startsWith('http')) {
            setModalDocument({ url, type: documentType });
            setModalVisible(true);
        } else {
            showToast(
                'error',
                'Error',
                `${documentType} document not available or invalid.`
            );
        }
    };

    const handleViewDetails = (data) => {
        setSelectedApplication(data);
        setDetailsModalVisible(true);
    };

    const handleEdit = (data) => {
        router.push({
            pathname: '/Broker/BrokerForm',
            params: { editData: JSON.stringify(data) },
        });
    };

    const getStatusStyle = (status) => {
        switch (status.toLowerCase()) {
            case 'approved':
                return [styles.statusBadge, styles.approvedBadge];
            case 'pending':
                return [styles.statusBadge, styles.pendingBadge];
            case 'rejected':
                return [styles.statusBadge, styles.rejectedBadge];
            default:
                return [styles.statusBadge, styles.pendingBadge];
        }
    };

    const renderHeader = () => (
        <>
            <View style={styles.headerContainer}>
                <BackButton
                    color={theme.WHITE}
                    onPress={() => router.push('./Profile')}
                />
                <Text style={[styles.headerTitle, { color: theme.WHITE }]}>
                    Applications
                </Text>
            </View>
            <View style={styles.backgroundOverlay}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                    style={styles.gradientOverlay}
                />
            </View>
        </>
    );

    const renderItem = ({ item }) => {
        if (item.type === 'no-data') {
            return (
                <View
                    style={[
                        styles.cardContainer,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <Text
                        style={[
                            styles.noDataText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        No applications or requests available.
                    </Text>
                    {user?.role.toLowerCase() === 'user' && (
                        <TouchableOpacity
                            style={styles.submitButton}
                            onPress={() => router.push('/Broker/BrokerForm')}
                            accessibilityLabel="Submit new application"
                        >
                            <LinearGradient
                                colors={[theme.PRIMARY, theme.SECONDARY]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={styles.submitButtonGradient}
                            >
                                <Text
                                    style={[
                                        styles.submitButtonText,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Submit New Application
                                </Text>
                            </LinearGradient>
                        </TouchableOpacity>
                    )}
                </View>
            );
        }

        if (item.type === 'brokers' || item.type === 'contractors') {
            return (
                <View
                    style={[
                        styles.cardContainer,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Your Submitted Application
                    </Text>
                    {applications.brokers.map((app, idx) => (
                        <Animated.View
                            key={app.id}
                            style={[
                                styles.applicationCard,
                                {
                                    backgroundColor: theme.CARD,
                                    borderWidth: 0.5,
                                    borderColor: theme.GRAY_LIGHT,
                                },
                            ]}
                            entering={FadeInUp.delay(idx * 100).duration(300)}
                        >
                            <View
                                style={{
                                    flexDirection: 'column',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    paddingHorizontal: 12,
                                    paddingVertical: 8,
                                }}
                            >
                                <View style={styles.cardContent}>
                                    <Text
                                        style={[
                                            styles.cardTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        {app.nameOnAadhaar}
                                    </Text>
                                    <View style={getStatusStyle(app.status)}>
                                        <Text
                                            style={[
                                                styles.statusText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            {app.status
                                                .charAt(0)
                                                .toUpperCase() +
                                                app.status.slice(1)}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                            <View style={styles.cardActions}>
                                <TouchableOpacity
                                    onPress={() => handleViewDetails(app)}
                                    style={styles.actionButton}
                                    accessibilityLabel="View application details"
                                    testID={`view-details-${app.id}`}
                                >
                                    <MaterialIcons
                                        name="visibility"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => handleEdit(app)}
                                    style={styles.actionButton}
                                    accessibilityLabel="Edit application"
                                    testID={`edit-application-${app.id}`}
                                >
                                    <MaterialIcons
                                        name="edit"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                </TouchableOpacity>
                            </View>
                        </Animated.View>
                    ))}
                </View>
            );
        }

        if (item.type === 'requests') {
            return (
                <View
                    style={[
                        styles.cardContainer,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        {user?.role.toLowerCase() === 'broker'
                            ? 'Site Verification Requests'
                            : 'Construction Requests'}
                    </Text>
                    {applications.requests.map((req, idx) => (
                        <Animated.View
                            key={req.id}
                            style={[
                                styles.applicationCard,
                                {
                                    backgroundColor: theme.CARD,
                                    borderWidth: 1.5,
                                    borderColor: theme.PRIMARY,
                                },
                            ]}
                            entering={FadeInUp.delay(idx * 100).duration(300)}
                        >
                            <View style={styles.cardContent}>
                                <Text
                                    style={[
                                        styles.cardTitle,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    {req.title || `Request ${req.id}`}
                                </Text>
                                <View style={getStatusStyle(req.status)}>
                                    <Text
                                        style={[
                                            styles.statusText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        {req.status.charAt(0).toUpperCase() +
                                            req.status.slice(1)}
                                    </Text>
                                </View>
                            </View>
                            <View style={styles.cardActions}>
                                <TouchableOpacity
                                    onPress={() => handleViewDetails(req)}
                                    style={styles.actionButton}
                                    accessibilityLabel="View request details"
                                    testID={`view-request-${req.id}`}
                                >
                                    <MaterialIcons
                                        name="visibility"
                                        size={20}
                                        color={
                                            theme.ACTION_BUTTON ||
                                            (isDarkMode
                                                ? theme.WARNING
                                                : theme.PRIMARY)
                                        }
                                    />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => handleEdit(req)}
                                    style={styles.actionButton}
                                    accessibilityLabel="Edit request"
                                    testID={`edit-request-${req.id}`}
                                >
                                    <MaterialIcons
                                        name="edit"
                                        size={20}
                                        color={
                                            theme.ACTION_BUTTON ||
                                            (isDarkMode
                                                ? theme.WARNING
                                                : theme.PRIMARY)
                                        }
                                    />
                                </TouchableOpacity>
                            </View>
                        </Animated.View>
                    ))}
                </View>
            );
        }

        return null;
    };

    if (isFetching || userError || brokerError) {
        return (
            <View
                style={[
                    styles.loadingContainer,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                />
                <Animated.View entering={FadeInUp.duration(300)}>
                    <ActivityIndicator size="large" color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.loadingText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        {userError || brokerError
                            ? 'Failed to load data. Please try again.'
                            : 'Loading...'}
                    </Text>
                </Animated.View>
            </View>
        );
    }

    const data = section ? [{ type: section }] : [{ type: 'no-data' }];

    return (
        <View style={[styles.safe, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />
            <FlatList
                ref={flatListRef}
                data={data}
                renderItem={renderItem}
                keyExtractor={(item) => item.type}
                ListHeaderComponent={renderHeader}
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[theme.PRIMARY]}
                        tintColor={theme.PRIMARY}
                    />
                }
            />
            <DocumentPreviewModal
                visible={modalVisible}
                documentUrl={modalDocument.url}
                documentType={modalDocument.type}
                onClose={() => setModalVisible(false)}
            />
            <ApplicationDetailsModal
                visible={detailsModalVisible}
                onClose={() => setDetailsModalVisible(false)}
                data={selectedApplication}
                onPreview={handlePreview}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    safe: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        zIndex: 1,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginLeft: 12,
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.3,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    gradientOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity: 0.7,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
        paddingTop: 80,
    },
    cardContainer: {
        width: '95%',
        maxWidth: 400,
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 2,
        marginHorizontal: '2.5%',
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    applicationCard: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderRadius: 12,
        padding: 12,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 2,
    },
    cardContent: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    cardTitle: {
        fontSize: 16,
        fontWeight: '500',
        marginRight: 12,
    },
    cardActions: {
        flexDirection: 'row',
    },
    actionButton: {
        padding: 8,
    },
    statusBadge: {
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 12,
    },
    approvedBadge: {
        backgroundColor: '#4CAF50',
    },
    pendingBadge: {
        backgroundColor: '#FFC107',
    },
    rejectedBadge: {
        backgroundColor: '#F44336',
    },
    statusText: {
        fontSize: 12,
        fontWeight: '600',
    },
    noDataText: {
        fontSize: 16,
        textAlign: 'center',
        marginVertical: 16,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 16,
        marginTop: 8,
    },
    submitButton: {
        borderRadius: 12,
        marginTop: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    submitButtonGradient: {
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 12,
        alignItems: 'center',
    },
    submitButtonText: {
        fontSize: 16,
        fontWeight: '500',
    },
});
