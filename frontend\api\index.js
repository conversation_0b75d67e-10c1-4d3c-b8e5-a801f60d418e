import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { showToast } from '../utils/showToast';

// Base URL for API
const API_BASE_URL = 'https://api.buildconnect.com/api/v1'; // Replace with your actual API URL

// Create a public client (no auth required)
export const publicAPIClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Create a private client (auth required)
export const privateAPIClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to private client to add auth token
privateAPIClient.interceptors.request.use(
  async (config) => {
    const token = await SecureStore.getItemAsync('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle auth errors
privateAPIClient.interceptors
</augment_code_snippet>
