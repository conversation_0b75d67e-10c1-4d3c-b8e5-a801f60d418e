import React, { useContext } from 'react';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';

export default function AppLayout() {
  const { theme } = useContext(ThemeContext);
  const { user } = useContext(AuthContext);
  
  // Default to 'user' role if not available
  const userRole = user?.role?.toLowerCase() || 'user';
  
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.PRIMARY,
        tabBarInactiveTintColor: theme.TEXT_SECONDARY,
        tabBarStyle: { 
          backgroundColor: theme.CARD,
          borderTopColor: theme.BORDER,
        },
        headerStyle: {
          backgroundColor: theme.CARD,
        },
        headerTintColor: theme.TEXT_PRIMARY,
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      
      {/* User-specific tabs */}
      {userRole === 'user' && (
        <>
          <Tabs.Screen
            name="Land"
            options={{
              title: 'Land',
              tabBarIcon: ({ color, size }) => (
                <Ionicons name="business-outline" size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="Contractors"
            options={{
              title: 'Contractors',
              tabBarIcon: ({ color, size }) => (
                <Ionicons name="construct-outline" size={size} color={color} />
              ),
            }}
          />
        </>
      )}
      
      {/* Broker-specific tabs */}
      {userRole === 'broker' && (
        <>
          <Tabs.Screen
            name="Verification"
            options={{
              title: 'Verify',
              tabBarIcon: ({ color, size }) => (
                <Ionicons name="checkmark-circle-outline" size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="Projects"
            options={{
              title: 'Projects',
              tabBarIcon: ({ color, size }) => (
                <Ionicons name="briefcase-outline" size={size} color={color} />
              ),
            }}
          />
        </>
      )}
      
      {/* Contractor-specific tabs */}
      {userRole === 'contractor' && (
        <Tabs.Screen
          name="Projects"
          options={{
            title: 'Projects',
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="briefcase-outline" size={size} color={color} />
            ),
          }}
        />
      )}
      
      {/* Admin-specific tabs */}
      {userRole === 'admin' && (
        <>
          <Tabs.Screen
            name="Users"
            options={{
              title: 'Users',
              tabBarIcon: ({ color, size }) => (
                <Ionicons name="people-outline" size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="Management"
            options={{
              title: 'Manage',
              tabBarIcon: ({ color, size }) => (
                <Ionicons name="settings-outline" size={size} color={color} />
              ),
            }}
          />
        </>
      )}
      
      {/* Common tabs for all roles */}
      <Tabs.Screen
        name="Profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}