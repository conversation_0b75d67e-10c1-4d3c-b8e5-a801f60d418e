import React, { useContext, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';
import { INPUT_PATTERNS, TYPOGRAPHY_PATTERNS } from './StandardStyles';

/**
 * StandardInput - A reusable input component following BuildConnect design patterns
 * 
 * This component provides consistent input styling across the app based on patterns from:
 * - Login page
 * - BrokerForm page
 * - Profile pages
 * 
 * @param {Object} props
 * @param {string} props.label - Input label (optional)
 * @param {string} props.placeholder - Input placeholder
 * @param {string} props.value - Input value
 * @param {function} props.onChangeText - Value change handler
 * @param {string} props.iconName - Ionicons icon name (optional)
 * @param {string} props.iconType - Icon type: 'outline' or 'filled' (default: 'outline')
 * @param {boolean} props.secureTextEntry - Whether input is password type
 * @param {string} props.keyboardType - Keyboard type
 * @param {boolean} props.multiline - Whether input is multiline
 * @param {number} props.numberOfLines - Number of lines for multiline input
 * @param {string} props.error - Error message to display
 * @param {boolean} props.disabled - Whether input is disabled
 * @param {Object} props.containerStyle - Additional container styles
 * @param {Object} props.inputStyle - Additional input styles
 * @param {function} props.onFocus - Focus handler
 * @param {function} props.onBlur - Blur handler
 * @param {boolean} props.autoCapitalize - Auto capitalize setting
 * @param {boolean} props.autoCorrect - Auto correct setting
 */
const StandardInput = ({
  label,
  placeholder,
  value,
  onChangeText,
  iconName,
  iconType = 'outline',
  secureTextEntry = false,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
  error,
  disabled = false,
  containerStyle = {},
  inputStyle = {},
  onFocus,
  onBlur,
  autoCapitalize = 'none',
  autoCorrect = false,
  ...props
}) => {
  const { theme } = useContext(ThemeContext);
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleFocus = (e) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const getIconName = () => {
    if (!iconName) return null;
    return iconType === 'filled' ? iconName : `${iconName}-outline`;
  };

  const getContainerStyle = () => {
    const baseStyle = {
      ...INPUT_PATTERNS.CONTAINER,
      borderColor: error 
        ? '#FF3B30' 
        : isFocused 
          ? theme.PRIMARY 
          : theme.INPUT_BORDER,
      backgroundColor: disabled 
        ? theme.INPUT_BACKGROUND + '50' 
        : theme.INPUT_BACKGROUND,
    };

    if (error) {
      baseStyle.borderWidth = 2;
    }

    if (multiline) {
      baseStyle.alignItems = 'flex-start';
      baseStyle.paddingVertical = 16;
    }

    return [baseStyle, containerStyle];
  };

  const getInputStyle = () => {
    const baseStyle = {
      ...INPUT_PATTERNS.TEXT_INPUT,
      color: disabled ? theme.TEXT_SECONDARY : theme.TEXT_PRIMARY,
    };

    if (multiline) {
      baseStyle.minHeight = numberOfLines * 20;
      baseStyle.textAlignVertical = 'top';
    }

    return [baseStyle, inputStyle];
  };

  return (
    <View style={styles.wrapper}>
      {/* Label */}
      {label && (
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          {label}
        </Text>
      )}

      {/* Input Container */}
      <View style={getContainerStyle()}>
        {/* Leading Icon */}
        {iconName && (
          <Ionicons
            name={getIconName()}
            size={22}
            color={error ? '#FF3B30' : isFocused ? theme.PRIMARY : theme.TEXT_SECONDARY}
            style={INPUT_PATTERNS.ICON}
          />
        )}

        {/* Text Input */}
        <TextInput
          style={getInputStyle()}
          placeholder={placeholder}
          placeholderTextColor={theme.TEXT_SECONDARY}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={secureTextEntry && !showPassword}
          keyboardType={keyboardType}
          multiline={multiline}
          numberOfLines={multiline ? numberOfLines : 1}
          editable={!disabled}
          autoCapitalize={autoCapitalize}
          autoCorrect={autoCorrect}
          {...props}
        />

        {/* Password Toggle */}
        {secureTextEntry && (
          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={styles.passwordToggle}
            disabled={disabled}
          >
            <Ionicons
              name={showPassword ? 'eye-off-outline' : 'eye-outline'}
              size={22}
              color={theme.TEXT_SECONDARY}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Error Message */}
      {error && (
        <Text style={styles.errorText}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: 16,
  },
  
  label: {
    ...TYPOGRAPHY_PATTERNS.BODY_TEXT,
    fontWeight: '600',
    marginBottom: 8,
  },
  
  passwordToggle: {
    padding: 4,
  },
  
  errorText: {
    ...TYPOGRAPHY_PATTERNS.CAPTION,
    color: '#FF3B30',
    marginTop: 4,
    marginLeft: 4,
  },
});

export default StandardInput;
