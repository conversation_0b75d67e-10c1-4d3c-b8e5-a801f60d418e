import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  SafeAreaView,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

const SavedProperties = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState('all');

  // Mock saved properties data
  const [savedProperties, setSavedProperties] = useState([
    {
      id: 1,
      title: 'Luxury Villa in Green Valley',
      location: 'Sector 15, Gurgaon',
      price: '₹2.5 Cr',
      area: '3500 sq ft',
      bedrooms: 4,
      bathrooms: 3,
      type: 'Villa',
      image: 'https://via.placeholder.com/300x200',
      savedDate: '2024-01-15',
      broker: 'Elite Properties',
      rating: 4.8,
      features: ['Swimming Pool', 'Garden', 'Parking'],
    },
    {
      id: 2,
      title: 'Modern 3BHK Apartment',
      location: 'Bandra West, Mumbai',
      price: '₹1.8 Cr',
      area: '1200 sq ft',
      bedrooms: 3,
      bathrooms: 2,
      type: 'Apartment',
      image: 'https://via.placeholder.com/300x200',
      savedDate: '2024-01-12',
      broker: 'Prime Realty',
      rating: 4.6,
      features: ['Gym', 'Security', 'Lift'],
    },
    {
      id: 3,
      title: 'Commercial Plot',
      location: 'Electronic City, Bangalore',
      price: '₹85 Lakh',
      area: '2000 sq ft',
      bedrooms: 0,
      bathrooms: 0,
      type: 'Plot',
      image: 'https://via.placeholder.com/300x200',
      savedDate: '2024-01-10',
      broker: 'Land Deals',
      rating: 4.5,
      features: ['Corner Plot', 'Main Road', 'Clear Title'],
    },
  ]);

  const tabs = [
    { id: 'all', name: 'All Properties', count: savedProperties.length },
    { id: 'Villa', name: 'Villas', count: savedProperties.filter(p => p.type === 'Villa').length },
    { id: 'Apartment', name: 'Apartments', count: savedProperties.filter(p => p.type === 'Apartment').length },
    { id: 'Plot', name: 'Plots', count: savedProperties.filter(p => p.type === 'Plot').length },
  ];

  const filteredProperties = savedProperties.filter(property => {
    if (selectedTab === 'all') return true;
    return property.type === selectedTab;
  });

  const removeFromSaved = (propertyId) => {
    Alert.alert(
      'Remove Property',
      'Are you sure you want to remove this property from saved list?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setSavedProperties(prev => prev.filter(p => p.id !== propertyId));
          },
        },
      ]
    );
  };

  const shareProperty = (property) => {
    Alert.alert(
      'Share Property',
      `Share "${property.title}" with others?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Share', onPress: () => console.log('Sharing property...') },
      ]
    );
  };

  const renderProperty = (property) => (
    <TouchableOpacity
      key={property.id}
      style={[styles.propertyCard, { backgroundColor: theme.CARD_BACKGROUND }]}
      onPress={() => router.push(`/Properties/PropertyDetails?id=${property.id}`)}
    >
      <View style={styles.imageContainer}>
        <Image source={{ uri: property.image }} style={styles.propertyImage} />
        <View style={styles.imageOverlay}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => shareProperty(property)}
          >
            <Ionicons name="share-outline" size={20} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.removeButton]}
            onPress={() => removeFromSaved(property.id)}
          >
            <Ionicons name="heart" size={20} color="#E91E63" />
          </TouchableOpacity>
        </View>
        <View style={[styles.typeTag, { backgroundColor: theme.PRIMARY }]}>
          <Text style={styles.typeText}>{property.type}</Text>
        </View>
      </View>

      <View style={styles.propertyInfo}>
        <Text style={[styles.propertyTitle, { color: theme.TEXT_PRIMARY }]}>
          {property.title}
        </Text>
        
        <View style={styles.locationRow}>
          <Ionicons name="location" size={16} color={theme.TEXT_SECONDARY} />
          <Text style={[styles.location, { color: theme.TEXT_SECONDARY }]}>
            {property.location}
          </Text>
        </View>

        <View style={styles.detailsRow}>
          <Text style={[styles.price, { color: theme.PRIMARY }]}>
            {property.price}
          </Text>
          <Text style={[styles.area, { color: theme.TEXT_SECONDARY }]}>
            {property.area}
          </Text>
        </View>

        {property.bedrooms > 0 && (
          <View style={styles.roomsRow}>
            <View style={styles.roomInfo}>
              <Ionicons name="bed" size={16} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.roomText, { color: theme.TEXT_SECONDARY }]}>
                {property.bedrooms} BHK
              </Text>
            </View>
            <View style={styles.roomInfo}>
              <Ionicons name="water" size={16} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.roomText, { color: theme.TEXT_SECONDARY }]}>
                {property.bathrooms} Bath
              </Text>
            </View>
          </View>
        )}

        <View style={styles.featuresContainer}>
          {property.features.slice(0, 2).map((feature, index) => (
            <View key={index} style={[styles.featureTag, { backgroundColor: theme.PRIMARY + '20' }]}>
              <Text style={[styles.featureText, { color: theme.PRIMARY }]}>
                {feature}
              </Text>
            </View>
          ))}
          {property.features.length > 2 && (
            <Text style={[styles.moreFeatures, { color: theme.TEXT_SECONDARY }]}>
              +{property.features.length - 2} more
            </Text>
          )}
        </View>

        <View style={styles.bottomRow}>
          <View style={styles.brokerInfo}>
            <Text style={[styles.brokerText, { color: theme.TEXT_SECONDARY }]}>
              by {property.broker}
            </Text>
            <View style={styles.ratingRow}>
              <Ionicons name="star" size={14} color="#FFD700" />
              <Text style={[styles.rating, { color: theme.TEXT_SECONDARY }]}>
                {property.rating}
              </Text>
            </View>
          </View>
          <Text style={[styles.savedDate, { color: theme.TEXT_SECONDARY }]}>
            Saved {new Date(property.savedDate).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Saved Properties</Text>
          <TouchableOpacity onPress={() => router.push('/Properties/PropertyFilters')}>
            <Ionicons name="filter" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.tabsContainer}
        contentContainerStyle={styles.tabsContent}
      >
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tab,
              {
                backgroundColor: selectedTab === tab.id ? theme.PRIMARY : theme.CARD_BACKGROUND,
              }
            ]}
            onPress={() => setSelectedTab(tab.id)}
          >
            <Text
              style={[
                styles.tabText,
                {
                  color: selectedTab === tab.id ? '#fff' : theme.TEXT_SECONDARY,
                }
              ]}
            >
              {tab.name}
            </Text>
            {tab.count > 0 && (
              <View style={[
                styles.tabBadge,
                {
                  backgroundColor: selectedTab === tab.id ? 'rgba(255,255,255,0.3)' : theme.PRIMARY,
                }
              ]}>
                <Text style={styles.tabBadgeText}>{tab.count}</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Properties List */}
      <ScrollView
        style={styles.propertiesList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.propertiesContent}
      >
        {filteredProperties.length > 0 ? (
          filteredProperties.map(renderProperty)
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="heart-outline" size={64} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
              No saved properties found
            </Text>
            <Text style={[styles.emptyStateSubtext, { color: theme.TEXT_SECONDARY }]}>
              Start exploring properties and save your favorites
            </Text>
            <TouchableOpacity
              style={[styles.exploreButton, { backgroundColor: theme.PRIMARY }]}
              onPress={() => router.push('/(tabs)/Listings')}
            >
              <Text style={styles.exploreButtonText}>Explore Properties</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  tabsContainer: {
    marginBottom: 10,
  },
  tabsContent: {
    paddingHorizontal: 20,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tabBadge: {
    marginLeft: 6,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  propertiesList: {
    flex: 1,
  },
  propertiesContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  propertyCard: {
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
  },
  propertyImage: {
    width: '100%',
    height: 200,
  },
  imageOverlay: {
    position: 'absolute',
    top: 12,
    right: 12,
    flexDirection: 'row',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  removeButton: {
    backgroundColor: 'rgba(233, 30, 99, 0.2)',
  },
  typeTag: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  propertyInfo: {
    padding: 16,
  },
  propertyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  location: {
    fontSize: 14,
    marginLeft: 4,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  area: {
    fontSize: 14,
  },
  roomsRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  roomInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  roomText: {
    fontSize: 14,
    marginLeft: 4,
  },
  featuresContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  featureText: {
    fontSize: 12,
    fontWeight: '600',
  },
  moreFeatures: {
    fontSize: 12,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  brokerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  brokerText: {
    fontSize: 12,
    marginRight: 8,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 12,
    marginLeft: 2,
  },
  savedDate: {
    fontSize: 12,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    marginBottom: 24,
  },
  exploreButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  exploreButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default SavedProperties;
