import React from 'react';
import {
    Modal,
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Image,
    StyleSheet,
    ActivityIndicator,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Formik } from 'formik';
import { validationSchema } from '../../../utils/validateSchema'; 
import { showToast } from '../../../utils/showToast';

const ProfileUpdateModal = ({
    isVisible,
    onClose,
    userData,
    onSave,
    theme,
    isLoading,
}) => {
    const handleImagePick = async (setFieldValue) => {
        try {
            const permission =
                await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (!permission.granted) {
                showToast(
                    'error',
                    'Error',
                    'Permission to access photos required'
                );
                return;
            }

            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ['images'],
                allowsEditing: true,
                aspect: [1, 1],
                quality: 0.8,
            });

            if (!result.canceled && result.assets?.length > 0) {
                const selected = result.assets[0];

                const file = {
                    uri: selected.uri,
                    name: selected.fileName || `avatar_${Date.now()}.jpg`,
                    type: selected.mimeType || 'image/jpeg',
                };

                setFieldValue('avatar', file);
                setFieldValue('avatarPreview', selected.uri);
            }
        } catch (error) {
            console.error('Image picker error:', error);
            showToast('error', 'Error', 'Failed to pick image');
        }
    };

    return (
        <Modal
            visible={isVisible}
            animationType="fade"
            transparent
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity
                    style={styles.overlayTouchable}
                    onPress={onClose}
                />
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={styles.keyboardAvoidingView}
                >
                    <View
                        style={[styles.modalContainer, { backgroundColor: theme.CARD }]}
                    >
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={onClose}
                        >
                            <Ionicons name="close" size={24} color={theme.PRIMARY} />
                        </TouchableOpacity>

                        <ScrollView
                            showsVerticalScrollIndicator={false}
                            contentContainerStyle={styles.scrollContent}
                        >
                            <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                                Edit Profile
                            </Text>

                            <Formik
                                initialValues={{
                                    name: userData.name || '',
                                    email: userData.email || '',
                                    phone: userData.phone || '',
                                    avatar: null,
                                    avatarPreview: userData.avatar || null,
                                }}
                                enableReinitialize
                                validationSchema={validationSchema}
                                onSubmit={(values, { setSubmitting }) => {
                                    const { avatarPreview, ...submitData } = values;
                                    onSave(submitData);
                                    onClose();
                                    setSubmitting(false);
                                }}
                            >
                                {({
                                    handleChange,
                                    handleBlur,
                                    handleSubmit,
                                    values,
                                    errors,
                                    touched,
                                    setFieldValue,
                                    isSubmitting,
                                }) => {
                                    const displayImage = values.avatarPreview || values.avatar;

                                    return (
                                        <>
                                            {/* Avatar Section */}
                                            <View style={styles.avatarSection}>
                                                <View style={styles.avatarContainer}>
                                                    <View
                                                        style={[
                                                            styles.avatarWrapper,
                                                            { backgroundColor: theme.PRIMARY },
                                                        ]}
                                                    >
                                                        {displayImage ? (
                                                            <Image
                                                                source={{ uri: displayImage }}
                                                                style={styles.avatar}
                                                            />
                                                        ) : (
                                                            <Text
                                                                style={[styles.avatarText, { color: theme.WHITE }]}
                                                            >
                                                                {values.name?.charAt(0).toUpperCase() || 'U'}
                                                            </Text>
                                                        )}
                                                    </View>
                                                    <TouchableOpacity
                                                        style={[
                                                            styles.cameraButton,
                                                            { backgroundColor: theme.WHITE },
                                                        ]}
                                                        onPress={() => handleImagePick(setFieldValue)}
                                                    >
                                                        <Ionicons
                                                            name="camera-outline"
                                                            size={20}
                                                            color={theme.PRIMARY}
                                                        />
                                                    </TouchableOpacity>
                                                </View>
                                                {touched.avatar && errors.avatar && (
                                                    <Text style={[styles.errorText, { color: theme.ERROR || '#FF3B30' }]}>
                                                        {errors.avatar}
                                                    </Text>
                                                )}
                                            </View>

                                            {/* Form Fields */}
                                            <View style={styles.formContainer}>
                                                {['name', 'email', 'phone'].map((field) => (
                                                    <View key={field} style={styles.inputWrapper}>
                                                        <Text
                                                            style={[
                                                                styles.label,
                                                                { color: theme.TEXT_SECONDARY },
                                                            ]}
                                                        >
                                                            {field.charAt(0).toUpperCase() + field.slice(1)}
                                                            {field === 'name' && <Text style={styles.required}> *</Text>}
                                                        </Text>
                                                        <TextInput
                                                            style={[
                                                                styles.input,
                                                                {
                                                                    borderColor: touched[field] && errors[field]
                                                                        ? (theme.ERROR || '#FF3B30')
                                                                        : theme.INPUT_BORDER,
                                                                    color: theme.TEXT_PRIMARY,
                                                                    backgroundColor: theme.INPUT_BACKGROUND || theme.CARD,
                                                                },
                                                            ]}
                                                            onChangeText={handleChange(field)}
                                                            onBlur={handleBlur(field)}
                                                            value={values[field]}
                                                            placeholder={`Enter your ${field}`}
                                                            placeholderTextColor={theme.TEXT_SECONDARY + '80'}
                                                            keyboardType={
                                                                field === 'email'
                                                                    ? 'email-address'
                                                                    : field === 'phone'
                                                                    ? 'phone-pad'
                                                                    : 'default'
                                                            }
                                                            autoCapitalize={field === 'email' ? 'none' : 'words'}
                                                            autoCorrect={field !== 'email'}
                                                        />
                                                        {touched[field] && errors[field] && (
                                                            <Text style={[styles.errorText, { color: theme.ERROR || '#FF3B30' }]}>
                                                                {errors[field]}
                                                            </Text>
                                                        )}
                                                    </View>
                                                ))}
                                            </View>

                                            {/* Submit Button */}
                                            <TouchableOpacity
                                                style={[
                                                    styles.saveButton,
                                                    {
                                                        backgroundColor: theme.PRIMARY,
                                                        opacity: isLoading || isSubmitting ? 0.6 : 1,
                                                    },
                                                ]}
                                                onPress={handleSubmit}
                                                disabled={isLoading || isSubmitting}
                                            >
                                                {isLoading || isSubmitting ? (
                                                    <ActivityIndicator size="small" color={theme.WHITE} />
                                                ) : (
                                                    <Text
                                                        style={[styles.saveButtonText, { color: theme.WHITE }]}
                                                    >
                                                        Save Changes
                                                    </Text>
                                                )}
                                            </TouchableOpacity>
                                        </>
                                    );
                                }}
                            </Formik>
                        </ScrollView>
                    </View>
                </KeyboardAvoidingView>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.7)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    keyboardAvoidingView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        paddingHorizontal: 20,
    },
    scrollContent: {
        flexGrow: 1,
        paddingBottom: 30,
    },
    overlayTouchable: {
        position: 'absolute',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
    },
    modalContainer: {
        width: '100%',
        maxWidth: 400,
        maxHeight: '90%',
        borderRadius: 24,
        padding: 28,
        elevation: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 12 },
        shadowOpacity: 0.4,
        shadowRadius: 20,
    },
    closeButton: {
        position: 'absolute',
        top: 20,
        right: 20,
        padding: 8,
        zIndex: 10,
        borderRadius: 20,
        backgroundColor: 'rgba(0,0,0,0.05)',
    },
    title: {
        fontSize: 24,
        fontWeight: '700',
        textAlign: 'center',
        marginBottom: 32,
        marginTop: 8,
    },
    avatarSection: {
        alignItems: 'center',
        marginBottom: 32,
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: 8,
    },
    avatarWrapper: {
        width: 100,
        height: 100,
        borderRadius: 50,
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        elevation: 6,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
    },
    avatar: {
        width: '100%',
        height: '100%',
    },
    avatarText: {
        fontSize: 38,
        fontWeight: 'bold',
    },
    cameraButton: {
        position: 'absolute',
        bottom: -8,
        right: -8,
        padding: 12,
        borderRadius: 24,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
    formContainer: {
        gap: 24,
        marginBottom: 32,
    },
    inputWrapper: {
        gap: 8,
    },
    label: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
    },
    required: {
        color: '#FF3B30',
        fontSize: 16,
    },
    input: {
        borderWidth: 1.5,
        borderRadius: 16,
        paddingHorizontal: 20,
        paddingVertical: 16,
        fontSize: 16,
        minHeight: 56,
        textAlignVertical: 'center',
    },
    errorText: {
        fontSize: 13,
        marginTop: 6,
        marginLeft: 4,
        fontWeight: '500',
    },
    saveButton: {
        marginTop: 8,
        paddingVertical: 16,
        paddingHorizontal: 32,
        borderRadius: 16,
        alignItems: 'center',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
    },
    saveButtonText: {
        fontSize: 17,
        fontWeight: '600',
    },
});

export default ProfileUpdateModal;
