// ProfileUpdateModal.jsx
import React from 'react';
import {
    Modal,
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Image,
    StyleSheet,
    ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { showToast } from '../../../utils/showToast';

const validationSchema = Yup.object().shape({
    name: Yup.string().required('Name is required'),
    email: Yup.string().email('Invalid email'),
    phone: Yup.string(),
});

const ProfileUpdateModal = ({ isVisible, onClose, userData, onSave, theme, isLoading }) => {
    const handleImagePick = async (setFieldValue) => {
        try {
            const permission = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (!permission.granted) {
                showToast('error', 'Error', 'Permission to access photos required');
                return;
            }
            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [1, 1],
                quality: 1,
                base64: true,
            });
            if (!result.canceled) {
                const image = result.assets[0];
                const file = {
                    uri: image.uri,
                    name: image.fileName || 'avatar.jpg',
                    type: image.type || 'image/jpeg',
                };
                setFieldValue('avatar', file); // avatar will be handled by backend
            }
        } catch (error) {
            showToast('error', 'Error', 'Failed to pick image');
        }
    };

    return (
        <Modal visible={isVisible} animationType="fade" transparent onRequestClose={onClose}>
            <View style={styles.overlay}>
                <TouchableOpacity style={styles.overlayTouchable} onPress={onClose} />
                <View style={[styles.modalContainer, { backgroundColor: theme.CARD }]}>
                    <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                        <Ionicons name="close" size={24} color={theme.PRIMARY} />
                    </TouchableOpacity>

                    <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>Edit Profile</Text>

                    <Formik
                        initialValues={userData}
                        enableReinitialize
                        validationSchema={validationSchema}
                        onSubmit={(values) => {
                            onSave(values);
                            onClose();
                        }}
                    >
                        {({ handleChange, handleBlur, handleSubmit, values, errors, touched, setFieldValue }) => (
                            <>
                                <View style={styles.avatarContainer}>
                                    <View style={[styles.avatarWrapper, { backgroundColor: theme.PRIMARY }]}>
                                        {values.avatar && typeof values.avatar === 'string' ? (
                                            <Image source={{ uri: values.avatar }} style={styles.avatar} />
                                        ) : (
                                            <Text style={[styles.avatarText, { color: theme.WHITE }]}> {values.name.charAt(0).toUpperCase()} </Text>
                                        )}
                                    </View>
                                    <TouchableOpacity
                                        style={[styles.cameraButton, { backgroundColor: theme.GRAY_LIGHT_SEMITRANSPARENT }]}
                                        onPress={() => handleImagePick(setFieldValue)}
                                    >
                                        <Ionicons name="camera-outline" size={20} color={theme.PRIMARY} />
                                    </TouchableOpacity>
                                </View>

                                <View style={styles.formContainer}>
                                    {['name', 'email', 'phone'].map((field) => (
                                        <View key={field} style={styles.inputWrapper}>
                                            <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}> {field.charAt(0).toUpperCase() + field.slice(1)} </Text>
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        borderColor: theme.INPUT_BORDER,
                                                        color: theme.TEXT_PRIMARY,
                                                        backgroundColor: theme.INPUT_BACKGROUND,
                                                    },
                                                ]}
                                                onChangeText={handleChange(field)}
                                                onBlur={handleBlur(field)}
                                                value={values[field]}
                                                placeholder={`Enter ${field}`}
                                                placeholderTextColor={theme.TEXT_SECONDARY + '80'}
                                                keyboardType={field === 'email' ? 'email-address' : field === 'phone' ? 'phone-pad' : 'default'}
                                            />
                                            {touched[field] && errors[field] && (
                                                <Text style={{ color: 'red', fontSize: 12 }}>{errors[field]}</Text>
                                            )}
                                        </View>
                                    ))}
                                </View>

                                <TouchableOpacity
                                    style={[styles.saveButton, { backgroundColor: theme.PRIMARY, opacity: isLoading ? 0.6 : 1 }]}
                                    onPress={handleSubmit}
                                    disabled={isLoading}
                                >
                                    {isLoading ? (
                                        <ActivityIndicator size="small" color={theme.WHITE} />
                                    ) : (
                                        <Text style={[styles.saveButtonText, { color: theme.WHITE }]}>Save Changes</Text>
                                    )}
                                </TouchableOpacity>
                            </>
                        )}
                    </Formik>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: { flex: 1, backgroundColor: 'rgba(0,0,0,0.6)', justifyContent: 'center', alignItems: 'center' },
    overlayTouchable: { position: 'absolute', top: 0, right: 0, bottom: 0, left: 0 },
    modalContainer: { width: '90%', maxWidth: 400, borderRadius: 16, padding: 24, elevation: 8 },
    closeButton: { position: 'absolute', top: 16, right: 16, padding: 8, zIndex: 1 },
    title: { fontSize: 22, fontWeight: '600', textAlign: 'center', marginBottom: 20 },
    avatarContainer: { alignItems: 'center', marginBottom: 20, position: 'relative' },
    avatarWrapper: { width: 90, height: 90, borderRadius: 45, justifyContent: 'center', alignItems: 'center', overflow: 'hidden' },
    avatar: { width: '100%', height: '100%' },
    avatarText: { fontSize: 36, fontWeight: 'bold' },
    cameraButton: { position: 'absolute', bottom: -10, right: -10, padding: 10, borderRadius: 30 },
    formContainer: { gap: 16 },
    inputWrapper: { gap: 8 },
    label: { fontSize: 16, fontWeight: '500' },
    input: { borderWidth: 1, borderRadius: 12, padding: 12, fontSize: 16 },
    saveButton: { marginTop: 20, padding: 14, borderRadius: 12, alignItems: 'center' },
    saveButtonText: { fontSize: 16, fontWeight: '600' },
});

export default ProfileUpdateModal;
