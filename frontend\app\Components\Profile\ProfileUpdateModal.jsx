// ProfileUpdateModal.jsx
import React from 'react';
import {
    Modal,
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Image,
    StyleSheet,
    ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { showToast } from '../../../utils/showToast';

const validationSchema = Yup.object().shape({
    name: Yup.string().required('Name is required'),
    email: Yup.string().email('Invalid email'),
    phone: Yup.string(),
});

const ProfileUpdateModal = ({
    isVisible,
    onClose,
    userData,
    onSave,
    theme,
    isLoading,
}) => {
    const handleImagePick = async (setFieldValue) => {
        try {
            const permission =
                await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (!permission.granted) {
                showToast(
                    'error',
                    'Error',
                    'Permission to access photos required'
                );
                return;
            }

            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ['images'],
                allowsEditing: true,
                aspect: [1, 1],
                quality: 0.8,
                allowsMultipleSelection: false,
            });

            if (!result.canceled && result.assets && result.assets.length > 0) {
                const image = result.assets[0];

                // Create file object for upload
                const file = {
                    uri: image.uri,
                    name: image.fileName || `avatar_${Date.now()}.jpg`,
                    type: image.mimeType || 'image/jpeg',
                };

                // Set the file object for upload and also set preview URI
                setFieldValue('avatar', file);
                setFieldValue('avatarPreview', image.uri);
            }
        } catch (error) {
            console.error('Image picker error:', error);
            showToast('error', 'Error', 'Failed to pick image');
        }
    };

    return (
        <Modal
            visible={isVisible}
            animationType="fade"
            transparent
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity
                    style={styles.overlayTouchable}
                    onPress={onClose}
                />
                <View
                    style={[
                        styles.modalContainer,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <TouchableOpacity
                        style={styles.closeButton}
                        onPress={onClose}
                    >
                        <Ionicons
                            name="close"
                            size={24}
                            color={theme.PRIMARY}
                        />
                    </TouchableOpacity>

                    <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                        Edit Profile
                    </Text>

                    <Formik
                        initialValues={{
                            ...userData,
                            avatarPreview: userData.avatar || null,
                        }}
                        enableReinitialize
                        validationSchema={validationSchema}
                        onSubmit={(values) => {
                            const { ...submitData } = values;
                            onSave(submitData);
                            onClose();
                        }}
                    >
                        {({
                            handleChange,
                            handleBlur,
                            handleSubmit,
                            values,
                            errors,
                            touched,
                            setFieldValue,
                        }) => {
                            const displayImage =
                                values.avatarPreview || values.avatar;
                            const showImage =
                                displayImage &&
                                typeof displayImage === 'string';

                            return (
                                <>
                                    <View style={styles.avatarContainer}>
                                        <View
                                            style={[
                                                styles.avatarWrapper,
                                                {
                                                    backgroundColor:
                                                        theme.PRIMARY,
                                                },
                                            ]}
                                        >
                                            {showImage ? (
                                                <Image
                                                    source={{
                                                        uri: displayImage,
                                                    }}
                                                    style={styles.avatar}
                                                    onError={() => {
                                                        setFieldValue(
                                                            'avatarPreview',
                                                            null
                                                        );
                                                    }}
                                                />
                                            ) : (
                                                <Text
                                                    style={[
                                                        styles.avatarText,
                                                        { color: theme.WHITE },
                                                    ]}
                                                >
                                                    {values.name
                                                        ? values.name
                                                              .charAt(0)
                                                              .toUpperCase()
                                                        : 'U'}
                                                </Text>
                                            )}
                                        </View>
                                        <TouchableOpacity
                                            style={[
                                                styles.cameraButton,
                                                {
                                                    backgroundColor:
                                                        theme.GRAY + '80',
                                                },
                                            ]}
                                            onPress={() =>
                                                handleImagePick(setFieldValue)
                                            }
                                        >
                                            <Ionicons
                                                name="camera-outline"
                                                size={20}
                                                color={theme.WHITE}
                                            />
                                        </TouchableOpacity>
                                    </View>

                                    <View style={styles.formContainer}>
                                        {['name', 'email', 'phone'].map(
                                            (field) => (
                                                <View
                                                    key={field}
                                                    style={styles.inputWrapper}
                                                >
                                                    <Text
                                                        style={[
                                                            styles.label,
                                                            {
                                                                color: theme.TEXT_SECONDARY,
                                                            },
                                                        ]}
                                                    >
                                                        {' '}
                                                        {field
                                                            .charAt(0)
                                                            .toUpperCase() +
                                                            field.slice(1)}{' '}
                                                    </Text>
                                                    <TextInput
                                                        style={[
                                                            styles.input,
                                                            {
                                                                borderColor:
                                                                    theme.INPUT_BORDER,
                                                                color: theme.TEXT_PRIMARY,
                                                                backgroundColor:
                                                                    theme.INPUT_BACKGROUND,
                                                            },
                                                        ]}
                                                        onChangeText={handleChange(
                                                            field
                                                        )}
                                                        onBlur={handleBlur(
                                                            field
                                                        )}
                                                        value={values[field]}
                                                        placeholder={`Enter ${field}`}
                                                        placeholderTextColor={
                                                            theme.TEXT_SECONDARY +
                                                            '80'
                                                        }
                                                        keyboardType={
                                                            field === 'email'
                                                                ? 'email-address'
                                                                : field ===
                                                                    'phone'
                                                                  ? 'phone-pad'
                                                                  : 'default'
                                                        }
                                                    />
                                                    {touched[field] &&
                                                        errors[field] && (
                                                            <Text
                                                                style={{
                                                                    color: 'red',
                                                                    fontSize: 12,
                                                                }}
                                                            >
                                                                {errors[field]}
                                                            </Text>
                                                        )}
                                                </View>
                                            )
                                        )}
                                    </View>

                                    <TouchableOpacity
                                        style={[
                                            styles.saveButton,
                                            {
                                                backgroundColor: theme.PRIMARY,
                                                opacity: isLoading ? 0.6 : 1,
                                            },
                                        ]}
                                        onPress={handleSubmit}
                                        disabled={isLoading}
                                    >
                                        {isLoading ? (
                                            <ActivityIndicator
                                                size="small"
                                                color={theme.WHITE}
                                            />
                                        ) : (
                                            <Text
                                                style={[
                                                    styles.saveButtonText,
                                                    { color: theme.WHITE },
                                                ]}
                                            >
                                                Save Changes
                                            </Text>
                                        )}
                                    </TouchableOpacity>
                                </>
                            );
                        }}
                    </Formik>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.6)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    overlayTouchable: {
        position: 'absolute',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
    },
    modalContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 16,
        padding: 24,
        elevation: 8,
    },
    closeButton: {
        position: 'absolute',
        top: 16,
        right: 16,
        padding: 8,
        zIndex: 1,
    },
    title: {
        fontSize: 22,
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 20,
    },
    avatarContainer: {
        alignItems: 'center',
        marginBottom: 20,
        position: 'relative',
    },
    avatarWrapper: {
        width: 90,
        height: 90,
        borderRadius: 45,
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
    },
    avatar: { width: '100%', height: '100%' },
    avatarText: { fontSize: 36, fontWeight: 'bold' },
    cameraButton: {
        position: 'absolute',
        bottom: -8,
        right: 85,
        padding: 10,
        borderRadius: 30,
    },
    formContainer: { gap: 16 },
    inputWrapper: { gap: 8 },
    label: { fontSize: 16, fontWeight: '500' },
    input: { borderWidth: 1, borderRadius: 12, padding: 12, fontSize: 16 },
    saveButton: {
        marginTop: 20,
        padding: 14,
        borderRadius: 12,
        alignItems: 'center',
    },
    saveButtonText: { fontSize: 16, fontWeight: '600' },
});

export default ProfileUpdateModal;
