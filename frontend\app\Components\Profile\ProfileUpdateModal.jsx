import React from 'react';
import {
    Modal,
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Image,
    StyleSheet,
    ActivityIndicator,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Formik } from 'formik';
import { validationSchema } from '../../../utils/validateSchema'; 
import { showToast } from '../../../utils/showToast';

const ProfileUpdateModal = ({
    isVisible,
    onClose,
    userData,
    onSave,
    theme,
    isLoading,
}) => {
    const handleImagePick = async (setFieldValue) => {
        try {
            const permission =
                await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (!permission.granted) {
                showToast(
                    'error',
                    'Error',
                    'Permission to access photos required'
                );
                return;
            }

            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ['images'],
                allowsEditing: true,
                aspect: [1, 1],
                quality: 0.8,
            });

            if (!result.canceled && result.assets?.length > 0) {
                const selected = result.assets[0];

                const file = {
                    uri: selected.uri,
                    name: selected.fileName || `avatar_${Date.now()}.jpg`,
                    type: selected.mimeType || 'image/jpeg',
                };

                setFieldValue('avatar', file);
                setFieldValue('avatarPreview', selected.uri);
            }
        } catch (error) {
            console.error('Image picker error:', error);
            showToast('error', 'Error', 'Failed to pick image');
        }
    };

    return (
        <Modal
            visible={isVisible}
            animationType="fade"
            transparent
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity
                    style={styles.overlayTouchable}
                    onPress={onClose}
                />
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={styles.keyboardAvoidingView}
                >
                    <View
                        style={[styles.modalContainer, { backgroundColor: theme.CARD }]}
                    >
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={onClose}
                        >
                            <Ionicons name="close" size={24} color={theme.PRIMARY} />
                        </TouchableOpacity>

                        <ScrollView
                            showsVerticalScrollIndicator={false}
                            contentContainerStyle={styles.scrollContent}
                        >
                            <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                                Edit Profile
                            </Text>

                    <Formik
    initialValues={{
        name: userData.name || '',
        email: userData.email || '',
        phone: userData.phone || '',
        avatar: null,
        avatarPreview: userData.avatar || null,
    }}
    enableReinitialize
    validationSchema={validationSchema}
    onSubmit={(values, { setSubmitting }) => {
        const { avatarPreview, ...submitData } = values;
        onSave(submitData); // avatar file is in submitData.avatar
        onClose();
        setSubmitting(false);
    }}
>
    {({
        handleChange,
        handleBlur,
        handleSubmit,
        values,
        errors,
        touched,
        setFieldValue,
        isSubmitting,
    }) => {
        const displayImage = values.avatarPreview || values.avatar;

        return (
            <>
                {/* Avatar Preview */}
                <View style={styles.avatarContainer}>
                    <View
                        style={[
                            styles.avatarWrapper,
                            { backgroundColor: theme.PRIMARY },
                        ]}
                    >
                        {displayImage ? (
                            <Image source={{ uri: displayImage }} style={styles.avatar} />
                        ) : (
                            <Text
                                style={[styles.avatarText, { color: theme.WHITE }]}
                            >
                                {values.name?.charAt(0).toUpperCase() || 'U'}
                            </Text>
                        )}
                    </View>
                    <TouchableOpacity
                        style={[
                            styles.cameraButton,
                            { backgroundColor: theme.GRAY_LIGHT_SEMITRANSPARENT || '#ccc' },
                        ]}
                        onPress={() => handleImagePick(setFieldValue)}
                    >
                        <Ionicons
                            name="camera-outline"
                            size={20}
                            color={theme.PRIMARY}
                        />
                    </TouchableOpacity>
                </View>
                {/* Avatar Error */}
                {touched.avatar && errors.avatar && (
                    <Text style={styles.errorText}>{errors.avatar}</Text>
                )}

                {/* Form Fields */}
                <View style={styles.formContainer}>
                    {['name', 'email', 'phone'].map((field) => (
                        <View key={field} style={styles.inputWrapper}>
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                {field.charAt(0).toUpperCase() + field.slice(1)}
                            </Text>
                            <TextInput
                                style={[
                                    styles.input,
                                    {
                                        borderColor: theme.INPUT_BORDER,
                                        color: theme.TEXT_PRIMARY,
                                        backgroundColor: theme.INPUT_BACKGROUND,
                                    },
                                ]}
                                onChangeText={handleChange(field)}
                                onBlur={handleBlur(field)}
                                value={values[field]}
                                placeholder={`Enter ${field}`}
                                placeholderTextColor={theme.TEXT_SECONDARY + '80'}
                                keyboardType={
                                    field === 'email'
                                        ? 'email-address'
                                        : field === 'phone'
                                        ? 'phone-pad'
                                        : 'default'
                                }
                            />
                            {touched[field] && errors[field] && (
                                <Text style={styles.errorText}>{errors[field]}</Text>
                            )}
                        </View>
                    ))}
                </View>

                {/* Submit Button */}
                <TouchableOpacity
                    style={[
                        styles.saveButton,
                        {
                            backgroundColor: theme.PRIMARY,
                            opacity: isLoading || isSubmitting ? 0.6 : 1,
                        },
                    ]}
                    onPress={handleSubmit}
                    disabled={isLoading || isSubmitting}
                >
                    {isLoading ? (
                        <ActivityIndicator size="small" color={theme.WHITE} />
                    ) : (
                        <Text
                            style={[styles.saveButtonText, { color: theme.WHITE }]}
                        >
                            Save Changes
                        </Text>
                    )}
                </TouchableOpacity>
            </>
        );
    }}
</Formik>
                        </ScrollView>
                    </View>
                </KeyboardAvoidingView>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.6)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    keyboardAvoidingView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
    },
    scrollContent: {
        flexGrow: 1,
        paddingBottom: 20,
    },
    overlayTouchable: {
        position: 'absolute',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
    },
    modalContainer: {
        width: '92%',
        maxWidth: 420,
        maxHeight: '85%',
        borderRadius: 20,
        padding: 24,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
    },
    closeButton: {
        position: 'absolute',
        top: 16,
        right: 16,
        padding: 8,
        zIndex: 1,
    },
    title: {
        fontSize: 22,
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 20,
    },
    avatarContainer: {
        alignItems: 'center',
        marginBottom: 20,
        position: 'relative',
    },
    avatarWrapper: {
        width: 90,
        height: 90,
        borderRadius: 45,
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
    },
    avatar: {
        width: '100%',
        height: '100%',
    },
    avatarText: {
        fontSize: 36,
        fontWeight: 'bold',
    },
    cameraButton: {
        position: 'absolute',
        bottom: -10,
        right: -10,
        padding: 10,
        borderRadius: 30,
    },
    formContainer: {
        gap: 20,
        marginTop: 8,
    },
    inputWrapper: {
        gap: 8,
    },
    label: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    input: {
        borderWidth: 1.5,
        borderRadius: 12,
        padding: 16,
        fontSize: 16,
        minHeight: 50,
        textAlignVertical: 'center',
    },
    errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
},
    saveButton: {
        marginTop: 20,
        padding: 14,
        borderRadius: 12,
        alignItems: 'center',
    },
    saveButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
});

export default ProfileUpdateModal;
