import {
    View,
    Text,
    Image,
    TouchableOpacity,
    Pressable,
    TextInput,
    StyleSheet,
    Dimensions,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    ActivityIndicator,
    Vibration,
} from 'react-native';
import { useState, useContext } from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { publicAPIClient } from '../../api/';
import BackButton from '../Components/Shared/BackButton';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const { height } = Dimensions.get('window');

export default function SignUp() {
    const { theme } = useContext(ThemeContext);
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState({
        name: '',
        email: '',
        phone: '',
        password: '',
    });

    const router = useRouter();

    // Email validation function
    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    // Phone validation function
    const validatePhone = (phone) => {
        const phoneRegex = /^[6-9]\d{9}$/;
        return phoneRegex.test(phone);
    };

    // Name validation function
    const validateName = (name) => {
        const nameRegex = /^[a-zA-Z\s]+$/;
        return nameRegex.test(name);
    };

    // Password strength evaluation
    const evaluatePasswordStrength = (password) => {
        if (!password) return { strength: 'none', score: 0 };

        let score = 0;

        // Length check
        if (password.length >= 8) score += 1;

        // Character variety checks
        if (/[A-Z]/.test(password)) score += 1; // Has uppercase
        if (/[a-z]/.test(password)) score += 1; // Has lowercase
        if (/[0-9]/.test(password)) score += 1; // Has number
        if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special char

        // Determine strength category
        let strength = 'weak';
        if (score >= 3) strength = 'medium';
        if (score >= 5) strength = 'strong';

        return { strength, score };
    };

    // Form validation function
    const validateForm = () => {
        let isValid = true;
        const newErrors = {
            name: '',
            email: '',
            phone: '',
            password: '',
        };

        // Name validation
        if (!name.trim()) {
            newErrors.name = 'Name is required';
            isValid = false;
        } else if (name.trim().length < 2) {
            newErrors.name = 'Name must be at least 2 characters';
            isValid = false;
        } else if (!validateName(name)) {
            newErrors.name = 'Name must not contain numbers';
            isValid = false;
        }

        // Email validation
        if (!email.trim()) {
            newErrors.email = 'Email is required';
            isValid = false;
        } else if (!validateEmail(email)) {
            newErrors.email = 'Please enter a valid email';
            isValid = false;
        }

        // Phone validation
        if (!phone.trim()) {
            newErrors.phone = 'Phone number is required';
            isValid = false;
        } else if (!validatePhone(phone)) {
            newErrors.phone =
                'Please enter a valid 10-digit Indian phone number';
            isValid = false;
        }

        // Password validation
        if (!password) {
            newErrors.password = 'Password is required';
            isValid = false;
        } else if (password.length < 8) {
            newErrors.password = 'Password must be at least 8 characters';
            isValid = false;
        } else if (
            !/[A-Z]/.test(password) ||
            !/[a-z]/.test(password) ||
            !/[0-9]/.test(password) ||
            !/[^A-Za-z0-9]/.test(password)
        ) {
            newErrors.password =
                'Password must include uppercase, lowercase, number and special character';
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async () => {
        // Validate form before submission
        if (!validateForm()) {
            Vibration.vibrate(200); // Provide haptic feedback for validation errors
            return;
        }

        setIsLoading(true);

        const data = {
            name,
            email,
            phone,
            password,
        };

        try {
            await publicAPIClient.post('/user-service/api/v1/signup', data);
            showToast(
                'success',
                'Success',
                'Your account has been created successfully!',
                'Login Now',
                '/auth/Login'
            );
            router.push('/auth/Login');
        } catch (e) {
            // Handle specific error cases
            if (e.response) {
                if (e.response.status === 409) {
                    // Account already exists
                    showToast(
                        'error',
                        'Registration Failed',
                        'An account with this email already exists.',
                        'OK',
                        '/auth/SignUp'
                    );
                } else if (e.response.status === 400) {
                    // Invalid data
                    showToast(
                        'error',
                        'Invalid Data',
                        e.response.data?.message ||
                            'Please check your information and try again.',
                        'OK',
                        '/auth/SignUp'
                    );
                } else {
                    // Registration error
                    showToast(
                        'error',
                        'Registration Error',
                        'An error occurred during registration. Please try again.',
                        'OK',
                        '/auth/SignUp'
                    );
                }
            } else if (e.request) {
                // Network error
                showToast(
                    'error',
                    'Network Error',
                    'Please check your internet connection and try again.'
                );
            } else {
                // Unknown error
                showToast(
                    'error',
                    'Error',
                    'An unexpected error occurred. Please try again.'
                );
            }

            Vibration.vibrate(200); // Provide haptic feedback for errors
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Logo */}
                    <View style={styles.logoContainer}>
                        <Image
                            source={require('../../assets/images/build-connect.jpg')}
                            style={[
                                styles.logo,
                                { borderColor: theme.LOGO_BORDER },
                            ]}
                            resizeMode="contain"
                        />
                    </View>

                    {/* Form Container */}
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Signup
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Create your new account
                        </Text>

                        {/* Name Input */}
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.name ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="person-outline"
                                size={22}
                                color={errors.name ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Name"
                                value={name}
                                onChangeText={(text) => {
                                    setName(text);
                                    if (errors.name) {
                                        setErrors({ ...errors, name: '' });
                                    }
                                }}
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                keyboardType="default"
                            />
                            {name.length > 0 && (
                                <TouchableOpacity
                                    onPress={() => setName('')}
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.name ? (
                            <Text style={styles.errorText}>{errors.name}</Text>
                        ) : null}

                        {/* Email Input */}
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.email ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="mail-outline"
                                size={22}
                                color={errors.email ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Email"
                                value={email}
                                onChangeText={(text) => {
                                    setEmail(text);
                                    if (errors.email) {
                                        setErrors({ ...errors, email: '' });
                                    }
                                }}
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                autoCapitalize="none"
                                keyboardType="email-address"
                                autoComplete="email"
                            />
                            {email.length > 0 && (
                                <TouchableOpacity
                                    onPress={() => setEmail('')}
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.email ? (
                            <Text style={styles.errorText}>{errors.email}</Text>
                        ) : null}

                        {/* Phone Input */}
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.phone ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="call-outline"
                                size={22}
                                color={errors.phone ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Phone"
                                value={phone}
                                onChangeText={(text) => {
                                    setPhone(text);
                                    if (errors.phone) {
                                        setErrors({ ...errors, phone: '' });
                                    }
                                }}
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                keyboardType="numeric"
                                maxLength={10}
                            />
                            {phone.length > 0 && (
                                <TouchableOpacity
                                    onPress={() => setPhone('')}
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.phone ? (
                            <Text style={styles.errorText}>{errors.phone}</Text>
                        ) : null}

                        {/* Password Input */}
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.password ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="lock-closed-outline"
                                size={22}
                                color={errors.password ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Password"
                                value={password}
                                onChangeText={(text) => {
                                    setPassword(text);
                                    if (errors.password) {
                                        setErrors({ ...errors, password: '' });
                                    }
                                }}
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                secureTextEntry={!showPassword}
                            />
                            <TouchableOpacity
                                onPress={() => setShowPassword(!showPassword)}
                                style={styles.passwordToggle}
                            >
                                <Ionicons
                                    name={
                                        showPassword
                                            ? 'eye-off-outline'
                                            : 'eye-outline'
                                    }
                                    size={22}
                                    color={
                                        errors.password ? 'red' : theme.PRIMARY
                                    }
                                />
                            </TouchableOpacity>
                        </View>
                        {errors.password ? (
                            <Text style={styles.errorText}>
                                {errors.password}
                            </Text>
                        ) : null}

                        {/* Password Strength Indicator */}
                        {password ? (
                            <View style={styles.passwordStrengthContainer}>
                                <View
                                    style={styles.passwordStrengthBarContainer}
                                >
                                    <View
                                        style={[
                                            styles.passwordStrengthBar,
                                            styles[
                                                `${evaluatePasswordStrength(password).strength}Password`
                                            ],
                                            {
                                                width: `${(evaluatePasswordStrength(password).score / 5) * 100}%`,
                                            },
                                        ]}
                                    />
                                </View>
                                <Text style={styles.passwordStrengthText}>
                                    {evaluatePasswordStrength(password)
                                        .strength.charAt(0)
                                        .toUpperCase() +
                                        evaluatePasswordStrength(
                                            password
                                        ).strength.slice(1)}{' '}
                                    password
                                </Text>
                                <Text style={styles.passwordRequirements}>
                                    Password must contain at least 12
                                    characters, including uppercase, lowercase,
                                    number and special character.
                                </Text>
                            </View>
                        ) : null}

                        {/* Signup Button */}
                        <TouchableOpacity
                            onPress={handleSubmit}
                            style={[
                                styles.signupButton,
                                { shadowColor: theme.PRIMARY },
                            ]}
                            disabled={isLoading}
                        >
                            <LinearGradient
                                colors={[theme.PRIMARY, theme.SECONDARY]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={styles.signupButtonGradient}
                            >
                                {isLoading ? (
                                    <View style={styles.loadingContainer}>
                                        <ActivityIndicator
                                            color="white"
                                            size="small"
                                        />
                                        <Text
                                            style={[
                                                styles.signupButtonText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            Signing up...
                                        </Text>
                                    </View>
                                ) : (
                                    <Text
                                        style={[
                                            styles.signupButtonText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Signup
                                    </Text>
                                )}
                            </LinearGradient>
                        </TouchableOpacity>

                        {/* Login Link */}
                        <View style={styles.signupContainer}>
                            <Text
                                style={[
                                    styles.signupText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Already have an account?
                            </Text>
                            <Pressable
                                onPress={() => router.push('/auth/Login')}
                            >
                                <Text
                                    style={[
                                        styles.signupLink,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Login Here
                                </Text>
                            </Pressable>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
    },
    logoContainer: {
        alignItems: 'center',
        marginTop: height * -0.045,
        marginBottom: height * 0.03,
    },
    logo: {
        width: 120,
        height: 120,
        borderWidth: 3,
        borderRadius: 60,
    },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 8,
        paddingHorizontal: 12,
        height: 56,
    },
    inputError: {
        borderColor: 'red',
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginBottom: 8,
        marginLeft: 4,
    },
    inputIcon: {
        marginRight: 12,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 16,
    },
    clearButton: {
        padding: 8,
    },
    passwordToggle: {
        padding: 8,
    },
    passwordStrengthContainer: {
        marginBottom: 16,
        width: '100%',
    },
    passwordStrengthBarContainer: {
        height: 4,
        backgroundColor: '#e0e0e0',
        borderRadius: 2,
        marginBottom: 6,
        width: '100%',
        overflow: 'hidden',
    },
    passwordStrengthBar: {
        height: '100%',
        borderRadius: 2,
        transition: 'width 0.3s',
    },
    weakPassword: {
        backgroundColor: '#FF6347', // Tomato red for weak passwords
    },
    mediumPassword: {
        backgroundColor: '#FFD700', // Gold for medium strength passwords
    },
    strongPassword: {
        backgroundColor: '#32CD32', // Lime green for strong passwords
    },
    passwordStrengthText: {
        fontSize: 15,
        color: '#666',
        marginBottom: 4,
    },
    passwordRequirements: {
        fontSize: 11,
        color: '#888',
        lineHeight: 14,
    },
    signupButton: {
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 14,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    signupButtonGradient: {
        paddingVertical: 16,
        alignItems: 'center',
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    signupButtonText: {
        fontSize: 18,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    signupContainer: {
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
    },
    signupText: {
        fontSize: 14,
        marginRight: 4,
    },
    signupLink: {
        fontSize: 14,
        fontWeight: 'bold',
    },
});
