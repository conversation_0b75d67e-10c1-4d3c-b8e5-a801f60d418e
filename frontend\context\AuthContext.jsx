import React, { createContext, useState, useEffect } from 'react';
import * as SecureStore from 'expo-secure-store';
import { useRouter, useSegments } from 'expo-router';
import { loginUser, logoutUser, getCurrentUser } from '../api/auth/authApi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { showToast } from '../utils/showToast';

export const AuthContext = createContext({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  logout: async () => {},
  register: async () => {},
});

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();
  const segments = useSegments();
  const queryClient = useQueryClient();

  // Check if user is authenticated on app load
  useEffect(() => {
    const checkAuth = async () => {
      const token = await SecureStore.getItemAsync('accessToken');
      setIsAuthenticated(!!token);
    };
    
    checkAuth();
  }, []);

  // Fetch user data if authenticated
  const { 
    data: user, 
    isLoading: isUserLoading,
    error: userError,
    refetch: refetchUser
  } = useQuery({
    queryKey: ['currentUser'],
    queryFn: getCurrentUser,
    enabled: isAuthenticated,
    onError: () => {
      // If we can't fetch the user, they're probably not authenticated
      setIsAuthenticated(false);
    }
  });

  // Handle routing based on authentication state
  useEffect(() => {
    const inAuthGroup = segments[0] === '(auth)';
    
    if (!isUserLoading) {
      if (!isAuthenticated && !inAuthGroup) {
        // Redirect to login if not authenticated and not in auth group
        router.replace('/Login');
      } else if (isAuthenticated && inAuthGroup) {
        // Redirect to home if authenticated but still in auth group
        router.replace('/');
      }
    }
  }, [isAuthenticated, segments, isUserLoading, router]);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: loginUser,
    onSuccess: (data) => {
      setIsAuthenticated(true);
      refetchUser();
      showToast('success', 'Success', 'Logged in successfully');
      router.replace('/');
    },
    onError: (error) => {
      showToast('error', 'Login Failed', error.response?.data?.message || 'Invalid credentials');
    }
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: logoutUser,
    onSuccess: () => {
      setIsAuthenticated(false);
      queryClient.clear();
      showToast('success', 'Success', 'Logged out successfully');
      router.replace('/Login');
    },
    onError: () => {
      showToast('error', 'Error', 'Failed to logout');
    }
  });

  const login = async (credentials) => {
    return loginMutation.mutate(credentials);
  };

  const logout = async () => {
    return logoutMutation.mutate();
  };

  const register = async (userData) => {
    // This would be implemented with a registration mutation
    // For now, just redirect to login
    router.replace('/Login');
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        isLoading: isUserLoading,
        login,
        logout,
        register,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

