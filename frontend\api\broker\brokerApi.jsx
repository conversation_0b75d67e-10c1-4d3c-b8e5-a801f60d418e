import { privateAPIClient } from '../index';

export const fetchBrokerProfile = async () => {
    const url = '/user-service/api/v1/brokers';
    const response = await privateAPIClient.get(url);
    return response.data.broker;
};

export const createBrokerApplication = async (data) => {
    const response = await privateAPIClient.post(
        '/user-service/api/v1/brokers',
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

export const updateBrokerProfile = async (id, data) => {
    const response = await privateAPIClient.patch(
        `/user-service/api/v1/brokers/${id}`,
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

// Fetch applications based on user role
export const fetchApplicationsByRole = async (role) => {
    let url;
    switch (role.toLowerCase()) {
        case 'user':
            // For users: fetch their submitted applications (broker/contractor applications)
            url = '/user-service/api/v1/user/applications';
            break;
        case 'broker':
            // For brokers: fetch pending site verification requests
            url = '/user-service/api/v1/brokers/pending-requests';
            break;
        case 'contractor':
            // For contractors: fetch pending construction requests
            url = '/user-service/api/v1/contractors/pending-requests';
            break;
        default:
            throw new Error(`Invalid role: ${role}`);
    }

    const response = await privateAPIClient.get(url);
    return response.data;
};

// Fetch user's submitted applications (for users)
export const fetchUserApplications = async () => {
    const response = await privateAPIClient.get('/user-service/api/v1/user/applications');
    return response.data.applications || [];
};

// Fetch pending site verification requests (for brokers)
export const fetchBrokerPendingRequests = async () => {
    const response = await privateAPIClient.get('/user-service/api/v1/brokers/pending-requests');
    return response.data.requests || [];
};

// Fetch pending construction requests (for contractors)
export const fetchContractorPendingRequests = async () => {
    const response = await privateAPIClient.get('/user-service/api/v1/contractors/pending-requests');
    return response.data.requests || [];
};
