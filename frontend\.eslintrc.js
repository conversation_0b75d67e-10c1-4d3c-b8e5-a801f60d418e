module.exports = {
    env: {
        browser: true,
        commonjs: true,
        es2021: true,
        node: true,
    },
    extends: [
        'expo',
        'eslint:recommended',
        'plugin:react-hooks/recommended',
        'plugin:prettier/recommended',
    ],
    overrides: [],
    parserOptions: {
        ecmaVersion: 'latest',
    },
    plugins: ['no-autofix'],
    rules: {
        'no-underscore-dangle': 0,
        'prettier/prettier': ['error'],
        'no-unused-vars': [1, { argsIgnorePattern: '^_' }],
        'prefer-const': 0,
        'no-autofix/prefer-const': 1,
        'func-names': [1, 'never'],
        'consistent-return': 0,
        'linebreak-style': 0,
        'no-console': ['error', { allow: ['error'] }],
    },
};
