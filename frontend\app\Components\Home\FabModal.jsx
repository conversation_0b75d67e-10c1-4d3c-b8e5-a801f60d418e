import React, { useContext } from 'react';
import {
    View,
    Modal,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    StyleSheet,
    Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { ThemeContext } from '../../../context/ThemeContext';

export default function FABModal({ modalVisible, setModalVisible }) {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const slideAnim = React.useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        if (modalVisible) {
            Animated.timing(slideAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }).start();
        }
    }, [modalVisible, slideAnim]);

    const slideUp = {
        transform: [
            {
                translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [300, 0],
                }),
            },
        ],
    };

    return (
        <Modal
            transparent
            animationType="none"
            visible={modalVisible}
            onRequestClose={() => setModalVisible(false)}
        >
            <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
                <View
                    style={[
                        styles.modalOverlay,
                        { backgroundColor: theme.BLACK + '80' },
                    ]}
                >
                    <TouchableWithoutFeedback>
                        <Animated.View
                            style={[
                                styles.modalContainer,
                                slideUp,
                                {
                                    backgroundColor: theme.CARD,
                                    shadowColor: theme.SHADOW,
                                },
                            ]}
                        >
                            {/* Header */}
                            <View style={styles.header}>
                                <Text
                                    style={[
                                        styles.headerText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Take Action
                                </Text>
                                <TouchableOpacity
                                    onPress={() => setModalVisible(false)}
                                >
                                    <Ionicons
                                        name="close"
                                        size={24}
                                        color={theme.TEXT_SECONDARY}
                                    />
                                </TouchableOpacity>
                            </View>

                            {/* Buttons */}
                            <TouchableOpacity
                                style={styles.button}
                                onPress={() => {
                                    setModalVisible(false);
                                    router.push('Properties');
                                }}
                                activeOpacity={0.8}
                            >
                                <LinearGradient
                                    colors={['#6366f1', '#a78bfa']}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    style={styles.gradient}
                                />
                                <View style={styles.buttonContent}>
                                    <Ionicons
                                        name="business-outline"
                                        size={24}
                                        color={theme.WHITE}
                                        style={styles.icon}
                                    />
                                    <Text
                                        style={[
                                            styles.text,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Sell Your Property
                                    </Text>
                                </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.button}
                                onPress={() => {
                                    setModalVisible(false);
                                    router.push('/Broker');
                                }}
                                activeOpacity={0.8}
                            >
                                <LinearGradient
                                    colors={['#14b8a6', '#34d399']}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    style={styles.gradient}
                                />
                                <View style={styles.buttonContent}>
                                    <MaterialIcons
                                        name="people-outline"
                                        size={24}
                                        color={theme.WHITE}
                                        style={styles.icon}
                                    />
                                    <Text
                                        style={[
                                            styles.text,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Become Site Scout
                                    </Text>
                                </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.button}
                                onPress={() => {
                                    setModalVisible(false);
                                    router.push('/Contractors');
                                }}
                                activeOpacity={0.8}
                            >
                                <LinearGradient
                                    colors={['#f59e0b', '#f97316']}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    style={styles.gradient}
                                />
                                <View style={styles.buttonContent}>
                                    <MaterialIcons
                                        name="engineering"
                                        size={24}
                                        color={theme.WHITE}
                                        style={styles.icon}
                                    />
                                    <Text
                                        style={[
                                            styles.text,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Become Contractor
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </Animated.View>
                    </TouchableWithoutFeedback>
                </View>
            </TouchableWithoutFeedback>
        </Modal>
    );
}

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'center',
    },
    modalContainer: {
        width: '90%',
        maxWidth: 400,
        padding: 20,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        borderBottomLeftRadius: 8,
        borderBottomRightRadius: 8,
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.2,
        shadowRadius: 12,
        elevation: 8,
        marginBottom: 20,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 10,
    },
    headerText: {
        fontSize: 20,
        fontWeight: '600',
    },
    button: {
        borderRadius: 12,
        overflow: 'hidden',
        marginVertical: 8,
        elevation: 4, // Subtle elevation for a lifted effect
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    buttonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
    },
    icon: {
        marginRight: 12,
    },
    text: {
        fontSize: 18,
        fontWeight: '600',
    },
});
