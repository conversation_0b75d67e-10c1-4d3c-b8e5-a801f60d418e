import React, { useContext, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  Switch,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateUserProfile } from '../../api/user/userApi';
import { showToast } from '../../utils/showToast';
import * as ImagePicker from 'expo-image-picker';

export default function ProfileScreen() {
  const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
  const { user, logout } = useContext(AuthContext);
  const router = useRouter();
  const queryClient = useQueryClient();
  
  const [isUploading, setIsUploading] = useState(false);
  
  const userRole = user?.role?.toLowerCase() || 'user';
  
  const { mutate: updateProfile, isLoading: isUpdating } = useMutation({
    mutationFn: updateUserProfile,
    onSuccess: () => {
      queryClient.invalidateQueries(['userProfile']);
      showToast('success', 'Success', 'Profile updated successfully');
    },
    onError: (error) => {
      showToast('error', 'Update Failed', error.response?.data?.message || 'Failed to update profile');
    }
  });
  
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: () => logout(),
          style: 'destructive',
        },
      ]
    );
  };
  
  const pickImage = async () => {
    try {
      setIsUploading(true);
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
        
        // Create form data for upload
        const formData = new FormData();
        formData.append('profileImage', {
          uri: selectedImage.uri,
          name: 'profile-image.jpg',
          type: 'image/jpeg',
        });
        
        // Update profile with new image
        updateProfile(formData);
      }
    } catch (error) {
      showToast('error', 'Error', 'Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };
  
  const navigateToRoleApplication = () => {
    if (userRole === 'user') {
      Alert.alert(
        'Choose Role',
        'Which role would you like to apply for?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Broker',
            onPress: () => router.push('/Broker'),
          },
          {
            text: 'Contractor',
            onPress: () => router.push('/Contractors'),
          },
        ]
      );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <View style={styles.profileImageContainer}>
          {isUploading ? (
            <View style={[styles.profileImage, { backgroundColor: 'rgba(255,255,255,0.2)' }]}>
              <ActivityIndicator color="#fff" size="large" />
            </View>
          ) : (
            <Image
              source={user?.profileImage ? { uri: user.profileImage } : require('../../assets/images/default-avatar.png')}
              style={styles.profileImage}
            />
          )}
          <TouchableOpacity style={styles.editImageButton} onPress={pickImage}>
            <Ionicons name="camera" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
        <Text style={styles.userName}>{user?.name || 'User'}</Text>
        <Text style={styles.userRole}>{userRole.charAt(0).toUpperCase() + userRole.slice(1)}</Text>
      </LinearGradient>
      
      <ScrollView style={styles.content}>
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Account</Text>
          
          <TouchableOpacity style={styles.menuItem} onPress={() => router.push('/Profile/EditProfile')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="person-outline" size={22} color={theme.PRIMARY} style={styles.menuIcon} />
              <Text style={[styles.menuText, { color: theme.TEXT_PRIMARY }]}>Edit Profile</Text>
            </View>
            <Ionicons name="chevron-forward" size={22} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem} onPress={() => router.push('/Profile/ChangePassword')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="lock-closed-outline" size={22} color={theme.PRIMARY} style={styles.menuIcon} />
              <Text style={[styles.menuText, { color: theme.TEXT_PRIMARY }]}>Change Password</Text>
            </View>
            <Ionicons name="chevron-forward" size={22} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
          
          {userRole === 'user' && (
            <TouchableOpacity style={styles.menuItem} onPress={navigateToRoleApplication}>
              <View style={styles.menuItemLeft}>
                <Ionicons name="briefcase-outline" size={22} color={theme.PRIMARY} style={styles.menuIcon} />
                <Text style={[styles.menuText, { color: theme.TEXT_PRIMARY }]}>Apply as Broker/Contractor</Text>
              </View>
              <Ionicons name="chevron-forward" size={22} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          )}
        </View>
        
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Preferences</Text>
          
          <View style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Ionicons 
                name={isDarkMode ? "moon-outline" : "sunny-outline"} 
                size={22} 
                color={theme.PRIMARY} 
                style={styles.menuIcon} 
              />
              <Text style={[styles.menuText, { color: theme.TEXT_PRIMARY }]}>Dark Mode</Text>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: '#767577', true: theme.PRIMARY }}
              thumbColor="#f4f3f4"
            />
          </View>
          
          <TouchableOpacity style={styles.menuItem} onPress={() => router.push('/Profile/Notifications')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="notifications-outline" size={22} color={theme.PRIMARY} style={styles.menuIcon} />
              <Text style={[styles.menuText, { color: theme.TEXT_PRIMARY }]}>Notifications</Text>
            </View>
            <Ionicons name="chevron-forward" size={22} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>
        
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Support</Text>
          
          <TouchableOpacity style={styles.menuItem} onPress={() => router.push('/Profile/Help')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="help-circle-outline" size={22} color={theme.PRIMARY} style={styles.menuIcon} />
              <Text style={[styles.menuText, { color: theme.TEXT_PRIMARY }]}>Help & Support</Text>
            </View>
            <Ionicons name="chevron-forward" size={22} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem} onPress={() => router.push('/Profile/About')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="information-circle-outline" size={22} color={theme.PRIMARY} style={styles.menuIcon} />
              <Text style={[styles.menuText, { color: theme.TEXT_PRIMARY }]}>About</Text>
            </View>
            <Ionicons name="chevron-forward" size={22} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity 
          style={[styles.logoutButton, { backgroundColor: theme.DANGER_LIGHT }]}
          onPress={handleLogout}
        >
          <Ionicons name="log-out-outline" size={22} color={theme.DANGER} style={styles.logoutIcon} />
          <Text style={[styles.logoutText, { color: theme.DANGER }]}>Logout</Text>
        </TouchableOpacity>
        
        <Text style={[styles.versionText, { color: theme.TEXT_SECONDARY }]}>Version 1.0.0</Text>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 15,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: '#fff',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  userRole: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    borderRadius: 10,
    marginBottom: 20,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    marginRight: 15,
  },
  menuText: {
    fontSize: 16,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  logoutIcon: {
    marginRight: 10,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
  },
  versionText: {
    textAlign: 'center',
    marginBottom: 30,
    fontSize: 14,
  },
});