import axios from 'axios';
import * as SecureStore from 'expo-secure-store';

export const publicAPIClient = axios.create({
    baseURL: 'http://192.168.1.37:8080',
});

export const privateAPIClient = axios.create({
    baseURL: 'http://192.168.1.37:8080',
});

privateAPIClient.interceptors.request.use(async (config) => {
    const token = await SecureStore.getItemAsync('accessToken');
    const sessionId = await SecureStore.getItemAsync('sessionId');
    if (token) config.headers.Authorization = `Bearer ${token}`;
    if (sessionId) config.headers.Session = sessionId;
    return config;
});

privateAPIClient.interceptors.response.use(
    (res) => res,
    async (error) => {
        const originalRequest = error.config;
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            const sessionId = await SecureStore.getItemAsync('sessionId');

            try {
                const { data } = await publicAPIClient.post(
                    '/users/refresh',
                    null,
                    {
                        headers: {
                            Session: sessionId,
                        },
                    }
                );

                const { accessToken } = data;
                await SecureStore.setItemAsync('accessToken', accessToken);
                return privateAPIClient(originalRequest);
            } catch (e) {
                return Promise.reject(e);
            }
        }
        return Promise.reject(error);
    }
);

publicAPIClient.interceptors.request.use((config) => {
    return config;
});
