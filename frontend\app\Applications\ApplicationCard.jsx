import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Animated, { FadeInUp } from 'react-native-reanimated';
import styles from './styles';

const getStatusStyle = (status) => {
    switch (status.toLowerCase()) {
        case 'approved':
            return [styles.statusBadge, styles.approvedBadge];
        case 'pending':
            return [styles.statusBadge, styles.pendingBadge];
        case 'rejected':
            return [styles.statusBadge, styles.rejectedBadge];
        default:
            return [styles.statusBadge, styles.pendingBadge];
    }
};

const ApplicationCard = ({
    item,
    index,
    theme,
    handleViewDetails,
    handleEdit,
    handleDelete,
    isRequests,
}) => {
    const title =
        item.type === 'brokers'
            ? item.nameOnAadhaar || item.panName
            : item.type === 'contractors'
              ? item.nameOnAadhaar || item.panName
              : item.title || `Request ${item.id}`;
    return (
        <Animated.View
            style={[
                styles.applicationCard,
                {
                    backgroundColor: theme.CARD,
                    borderWidth: isRequests ? 1.5 : 0.5,
                    borderColor: isRequests ? theme.PRIMARY : theme.GRAY_LIGHT,
                },
            ]}
            entering={FadeInUp.delay(index * 100).duration(300)}
        >
            <View style={styles.cardContent}>
                <Text style={[styles.cardTitle, { color: theme.TEXT_PRIMARY }]}>
                    {title}
                </Text>
                <View style={getStatusStyle(item.status)}>
                    <Text style={[styles.statusText, { color: theme.WHITE }]}>
                        {item.status.charAt(0).toUpperCase() +
                            item.status.slice(1)}
                    </Text>
                </View>
            </View>
            <View style={styles.cardActions}>
                <TouchableOpacity
                    onPress={() => handleViewDetails(item)}
                    style={styles.actionButton}
                    accessibilityLabel={`View details for ${title}`}
                    accessibilityRole="button"
                    testID={`view-details-${item.id}`}
                >
                    <MaterialIcons
                        name="visibility"
                        size={20}
                        color={theme.PRIMARY}
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    onPress={() => handleEdit(item)}
                    style={styles.actionButton}
                    accessibilityLabel={`Edit ${title}`}
                    accessibilityRole="button"
                    testID={`edit-item-${item.id}`}
                >
                    <MaterialIcons
                        name="edit"
                        size={20}
                        color={theme.PRIMARY}
                    />
                </TouchableOpacity>
                {!isRequests && (
                    <TouchableOpacity
                        onPress={() => handleDelete(item.id)}
                        style={styles.actionButton}
                        accessibilityLabel={`Delete ${title}`}
                        accessibilityRole="button"
                        testID={`delete-application-${item.id}`}
                    >
                        <MaterialIcons
                            name="delete"
                            size={20}
                            color={theme.PRIMARY}
                        />
                    </TouchableOpacity>
                )}
            </View>
        </Animated.View>
    );
};

export default ApplicationCard;
