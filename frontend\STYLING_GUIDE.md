# BuildConnect Styling Standardization Guide

## Overview

This document outlines the standardized styling patterns implemented across the BuildConnect React Native app. The patterns are based on analysis of existing components: `(tabs)/Profile.jsx`, `Broker/BrokerForm.jsx`, and `auth/Login.jsx`.

## Standardized Components Created

### 1. StandardStyles.js
**Location**: `frontend/app/Components/Shared/StandardStyles.js`

**Purpose**: Central styling constants and patterns
- Layout patterns (containers, backgrounds, cards)
- Gradient configurations
- Input styling patterns
- Button styling patterns
- Typography standards
- Spacing and border radius constants
- Shadow patterns
- Helper function `createStandardStyles(theme)`

### 2. StandardPageTemplate.jsx
**Location**: `frontend/app/Components/Shared/StandardPageTemplate.jsx`

**Purpose**: Reusable page template following BuildConnect design patterns
- Background image with gradient overlay
- Consistent card layouts
- Optional logo display
- Keyboard avoiding behavior
- Scrollable content support

**Usage Example**:
```jsx
<StandardPageTemplate
    title="Welcome Back"
    subtitle="Sign in to continue"
    showLogo={true}
    gradientColors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
>
    {/* Your page content */}
</StandardPageTemplate>
```

### 3. StandardInput.jsx
**Location**: `frontend/app/Components/Shared/StandardInput.jsx`

**Purpose**: Consistent input component with theme integration
- Icon support (leading icons)
- Password visibility toggle
- Error state handling
- Multiline support
- Disabled state styling
- Focus state management

**Usage Example**:
```jsx
<StandardInput
    label="Email"
    placeholder="Enter your email"
    value={email}
    onChangeText={setEmail}
    iconName="mail"
    keyboardType="email-address"
    error={emailError}
/>
```

### 4. StandardButton.jsx
**Location**: `frontend/app/Components/Shared/StandardButton.jsx`

**Purpose**: Consistent button component with multiple variants
- Variants: primary, secondary, outline, text, gradient
- Sizes: small, medium, large
- Loading state support
- Icon support (left/right positioning)
- Disabled state handling

**Usage Example**:
```jsx
<StandardButton
    title="Sign In"
    variant="primary"
    size="large"
    onPress={handleLogin}
    loading={isLoading}
    iconName="log-in"
    fullWidth={true}
/>
```

## Design Patterns Identified

### 1. Background Pattern
**Consistent across Login, Profile, BrokerForm**
- Background image: `../../assets/images/background.png`
- LinearGradient overlay with theme colors
- Standard gradient: `['rgba(42, 142, 158, 0.7)', theme.PRIMARY]`
- Gradient direction: top to bottom `{ x: 0, y: 0 }` to `{ x: 0, y: 1 }`

### 2. Card Layout Pattern
**Standard card dimensions and styling**
- Width: `90%` with `maxWidth: 400`
- Border radius: `20px`
- Padding: `24px`
- Elevation: `5` (Android shadow)
- Background: `theme.CARD`
- Shadow color: `theme.SHADOW`

### 3. Typography Hierarchy
- **Title**: 28px, bold, center-aligned, `theme.PRIMARY`
- **Subtitle**: 16px, center-aligned, `theme.TEXT_SECONDARY`, 80% opacity
- **Section Title**: 20px, semi-bold, `theme.TEXT_PRIMARY`
- **Body Text**: 16px, line height 24px, `theme.TEXT_PRIMARY`
- **Caption**: 14px, 70% opacity, `theme.TEXT_SECONDARY`

### 4. Input Pattern
- Border radius: `12px`
- Padding: `16px horizontal, 12px vertical`
- Border width: `1px` (2px for errors)
- Icon spacing: `12px` margin right
- Error color: `#FF3B30`
- Focus color: `theme.PRIMARY`

### 5. Button Pattern
- Border radius: `12px`
- Padding: `16px vertical, 24px horizontal`
- Font size: `16px`, weight: `600`
- Primary: `theme.PRIMARY` background, white text
- Secondary: transparent background, `theme.PRIMARY` border and text

## Updated Components

### 1. ChatScreen.jsx
**Changes Made**:
- Added theme integration with `ThemeContext`
- Implemented standardized header styling
- Used `SPACING`, `BORDER_RADIUS`, and `SHADOW_PATTERNS`
- Added proper StatusBar configuration
- Improved profile image styling with theme colors

### 2. Header.jsx
**Changes Made**:
- Replaced inline styles with standardized spacing constants
- Used `SPACING.MD` and `SPACING.SM` for consistent padding
- Applied `BORDER_RADIUS.ROUND` for circular elements
- Organized code structure with proper style objects

### 3. SearchBar.jsx
**Changes Made**:
- Converted to use `StandardInput` component
- Added theme integration
- Implemented proper search functionality with callback
- Used standardized spacing and shadow patterns
- Made component more reusable with props

## Implementation Guidelines

### For New Components
1. Import standardized components and styles:
   ```jsx
   import { createStandardStyles, SPACING, BORDER_RADIUS } from '../Shared/StandardStyles';
   import StandardPageTemplate from '../Shared/StandardPageTemplate';
   import StandardInput from '../Shared/StandardInput';
   import StandardButton from '../Shared/StandardButton';
   ```

2. Use `StandardPageTemplate` for full-page layouts
3. Use `StandardInput` for all form inputs
4. Use `StandardButton` for all interactive buttons
5. Apply consistent spacing using `SPACING` constants
6. Use `createStandardStyles(theme)` for theme-aware styling

### For Existing Components
1. Replace hardcoded colors with theme variables
2. Replace inline styles with standardized patterns
3. Use spacing constants instead of magic numbers
4. Apply consistent border radius and shadow patterns
5. Ensure proper theme integration

## Theme Integration

All standardized components integrate with the existing `ThemeContext`:
- `theme.PRIMARY` - Primary brand color
- `theme.CARD` - Card background color
- `theme.TEXT_PRIMARY` - Primary text color
- `theme.TEXT_SECONDARY` - Secondary text color
- `theme.INPUT_BORDER` - Input border color
- `theme.INPUT_BACKGROUND` - Input background color
- `theme.SHADOW` - Shadow color
- `theme.WHITE` - White color

## Next Steps

1. **Apply to Remaining Components**: Update all remaining components to use standardized patterns
2. **Create Additional Variants**: Add more button and input variants as needed
3. **Implement Loading States**: Add consistent loading indicators across components
4. **Add Animation Patterns**: Standardize animations and transitions
5. **Create Component Library**: Document all components for easy reference

## Benefits

- **Consistency**: Uniform look and feel across the entire app
- **Maintainability**: Centralized styling makes updates easier
- **Theme Support**: Proper dark/light mode integration
- **Reusability**: Components can be easily reused across different screens
- **Developer Experience**: Clear patterns make development faster
- **Quality**: Reduced styling bugs and inconsistencies
