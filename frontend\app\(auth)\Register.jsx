import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { Link, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { useMutation } from '@tanstack/react-query';
import { registerUser } from '../../api/auth/authApi';
import { showToast } from '../../utils/showToast';

export default function RegisterScreen() {
  const { theme, isDarkMode } = useContext(ThemeContext);
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const updateFormData = (key, value) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };
  
  const { mutate: register, isLoading } = useMutation({
    mutationFn: registerUser,
    onSuccess: () => {
      showToast('success', 'Success', 'Registration successful! Please login.');
      router.replace('/Login');
    },
    onError: (error) => {
      showToast('error', 'Registration Failed', error.response?.data?.message || 'Please try again');
    }
  });
  
  const handleRegister = () => {
    // Basic validation
    if (!formData.name || !formData.email || !formData.phone || !formData.password) {
      showToast('error', 'Validation Error', 'Please fill all required fields');
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      showToast('error', 'Validation Error', 'Passwords do not match');
      return;
    }
    
    // Submit registration
    register({
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      password: formData.password
    });
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Image 
            source={require('../../assets/images/build-connect.jpg')} 
            style={[styles.logo, { borderColor: theme.LOGO_BORDER }]}
            resizeMode="cover"
          />
          <Text style={[styles.appName, { color: theme.PRIMARY }]}>BuildConnect</Text>
        </View>

        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>Create Account</Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>Sign up to get started</Text>

        {/* Name Input */}
        <View style={[styles.inputContainer, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
          <Ionicons name="person-outline" size={20} color={theme.TEXT_SECONDARY} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: theme.TEXT_PRIMARY }]}
            placeholder="Full Name"
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={formData.name}
            onChangeText={(text) => updateFormData('name', text)}
          />
        </View>

        {/* Email Input */}
        <View style={[styles.inputContainer, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
          <Ionicons name="mail-outline" size={20} color={theme.TEXT_SECONDARY} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: theme.TEXT_PRIMARY }]}
            placeholder="Email"
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={formData.email}
            onChangeText={(text) => updateFormData('email', text)}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>

        {/* Phone Input */}
        <View style={[styles.inputContainer, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
          <Ionicons name="call-outline" size={20} color={theme.TEXT_SECONDARY} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: theme.TEXT_PRIMARY }]}
            placeholder="Phone Number"
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={formData.phone}
            onChangeText={(text) => updateFormData('phone', text)}
            keyboardType="phone-pad"
          />
        </View>

        {/* Password Input */}
        <View style={[styles.inputContainer, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
          <Ionicons name="lock-closed-outline" size={20} color={theme.TEXT_SECONDARY} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: theme.TEXT_PRIMARY }]}
            placeholder="Password"
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={formData.password}
            onChangeText={(text) => updateFormData('password', text)}
            secureTextEntry={!showPassword}
          />
          <TouchableOpacity 
            onPress={() => setShowPassword(!showPassword)}
            style={styles.eyeIcon}
          >
            <Ionicons 
              name={showPassword ? "eye-off-outline" : "eye-outline"} 
              size={20} 
              color={theme.TEXT_SECONDARY} 
            />
          </TouchableOpacity>
        </View>

        {/* Confirm Password Input */}
        <View style={[styles.inputContainer, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
          <Ionicons name="lock-closed-outline" size={20} color={theme.TEXT_SECONDARY} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: theme.TEXT_PRIMARY }]}
            placeholder="Confirm Password"
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={formData.confirmPassword}
            onChangeText={(text) => updateFormData('confirmPassword', text)}
            secureTextEntry={!showConfirmPassword}
          />
          <TouchableOpacity 
            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            style={styles.eyeIcon}
          >
            <Ionicons 
              name={showConfirmPassword ? "eye-off-outline" : "eye-outline"} 
              size={20} 
              color={theme.TEXT_SECONDARY} 
            />
          </TouchableOpacity>
        </View>

        <TouchableOpacity 
          onPress={handleRegister}
          disabled={isLoading}
        >
          <LinearGradient
            colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.registerButton}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.registerButtonText}>Register</Text>
            )}
          </LinearGradient>
        </TouchableOpacity>

        <View style={styles.loginContainer}>
          <Text style={[styles.loginText, { color: theme.TEXT_SECONDARY }]}>Already have an account? </Text>
          <Link href="/Login" asChild>
            <TouchableOpacity>
              <Text style={[styles.loginLink, { color: theme.PRIMARY }]}>Login</Text>
            </TouchableOpacity>
          </Link>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 2,
  },
  appName: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 25,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
  },
  eyeIcon: {
    padding: 10,
  },
  registerButton: {
    borderRadius: 8,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  loginText: {
    fontSize: 14,
  },
  loginLink: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});