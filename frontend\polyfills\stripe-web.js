// Web polyfill for Stripe React Native components
// This provides placeholder implementations for web builds

export const CardField = ({ style, onCardChange, ...props }) => {
  return null; // Return null for web - Stripe web integration would be different
};

export const StripeProvider = ({ children, ...props }) => {
  return children; // Just pass through children on web
};

export const useStripe = () => {
  return {
    createPaymentMethod: () => Promise.resolve({ error: { message: 'Stripe not available on web' } }),
    confirmPayment: () => Promise.resolve({ error: { message: 'Stripe not available on web' } }),
  };
};

export const useConfirmPayment = () => {
  return {
    confirmPayment: () => Promise.resolve({ error: { message: 'Stripe not available on web' } }),
  };
};

export default {
  Card<PERSON>ield,
  StripeProvider,
  useStripe,
  useConfirmPayment,
};
