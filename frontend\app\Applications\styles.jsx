import { StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export default StyleSheet.create({
    safe: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
        paddingBottom: 20,
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: width * 0.04,
        zIndex: 1,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginLeft: 12,
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.3,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    gradientOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity: 0.7,
    },
    cardContainer: {
        width: width * 0.95,
        maxWidth: 400,
        borderRadius: 12,
        padding: width * 0.04,
        marginHorizontal: width * 0.025,
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    applicationCard: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderRadius: 12,
        padding: 12,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 2,
    },
    cardContent: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    cardTitle: {
        fontSize: 16,
        fontWeight: '500',
        marginRight: 12,
        flexShrink: 1,
    },
    cardActions: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    actionButton: {
        padding: 8,
    },
    statusBadge: {
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 12,
    },
    approvedBadge: {
        backgroundColor: '#4CAF50',
    },
    pendingBadge: {
        backgroundColor: '#FFC107',
    },
    rejectedBadge: {
        backgroundColor: '#F44336',
    },
    statusText: {
        fontSize: 12,
        fontWeight: '600',
    },
    noDataText: {
        fontSize: 16,
        textAlign: 'center',
        marginVertical: 16,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorContainer: {
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 16,
        marginTop: 8,
        textAlign: 'center',
    },
    retryButton: {
        marginTop: 12,
        backgroundColor: '#4CAF50',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
    },
    retryButtonText: {
        fontSize: 16,
        fontWeight: '500',
    },
    submitButton: {
        borderRadius: 12,
        marginTop: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    submitButtonGradient: {
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 12,
        alignItems: 'center',
    },
    submitButtonText: {
        fontSize: 16,
        fontWeight: '500',
    },
    skeletonTitle: {
        width: '60%',
        height: 20,
        borderRadius: 4,
        marginBottom: 16,
    },
    skeletonText: {
        width: '70%',
        height: 16,
        borderRadius: 4,
        marginRight: 12,
    },
    skeletonBadge: {
        width: 80,
        height: 20,
        borderRadius: 12,
    },
});
